{"name": "taskflow-server", "version": "1.0.0", "description": "TaskFlow backend server with Express.js and Parse Server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix"}, "keywords": ["taskflow", "express", "parse-server", "mongodb", "rest-api"], "author": "TaskFlow Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "parse-server": "^6.4.0", "parse-dashboard": "^5.2.0", "mongoose": "^8.0.3", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "winston": "^3.11.0", "redis": "^4.6.10", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/taskflow/taskflow-server.git"}, "bugs": {"url": "https://github.com/taskflow/taskflow-server/issues"}, "homepage": "https://github.com/taskflow/taskflow-server#readme"}