# TaskFlow Server Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3000
HOST=localhost

# Parse Server Configuration
PARSE_APP_ID=taskflow-app-id
PARSE_MASTER_KEY=taskflow-master-key-change-in-production
PARSE_SERVER_URL=http://localhost:3000/parse
PARSE_MOUNT_PATH=/parse

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/taskflow
MONGODB_OPTIONS=retryWrites=true&w=majority

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# Email Configuration (for password reset, etc.)
EMAIL_PROVIDER=smtp
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# Push Notifications (APNs)
APNS_KEY_ID=your-apns-key-id
APNS_TEAM_ID=your-team-id
APNS_BUNDLE_ID=com.taskflow.app
APNS_PRIVATE_KEY_PATH=./certs/AuthKey.p8
APNS_PRODUCTION=false

# File Storage Configuration
FILE_STORAGE_TYPE=local
# For local storage
LOCAL_STORAGE_PATH=./uploads
# For cloud storage (AWS S3, etc.)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=taskflow-files

# Security Configuration
CORS_ORIGIN=http://localhost:3000,https://taskflow.app
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# Parse Dashboard Configuration (optional)
PARSE_DASHBOARD_USER=admin
PARSE_DASHBOARD_PASS=admin-password-change-in-production
PARSE_DASHBOARD_ALLOW_INSECURE_HTTP=true

# Development Configuration
DEBUG=taskflow:*
ENABLE_PARSE_DASHBOARD=true

# Production Configuration (uncomment for production)
# NODE_ENV=production
# PARSE_DASHBOARD_ALLOW_INSECURE_HTTP=false
# APNS_PRODUCTION=true
# LOG_LEVEL=warn
