/**
 * Parse Server Cloud Code
 * 服务器端业务逻辑和触发器
 */

const Parse = require('parse/node');

/**
 * 用户注册前触发器
 */
Parse.Cloud.beforeSave(Parse.User, async (request) => {
  const user = request.object;
  
  // 确保用户名唯一
  if (user.isNew() || user.dirtyKeys().includes('username')) {
    const username = user.get('username');
    if (username) {
      const query = new Parse.Query(Parse.User);
      query.equalTo('username', username);
      if (!user.isNew()) {
        query.notEqualTo('objectId', user.id);
      }
      const existingUser = await query.first({ useMasterKey: true });
      if (existingUser) {
        throw new Parse.Error(Parse.Error.USERNAME_TAKEN, 'Username already exists');
      }
    }
  }
  
  // 确保邮箱唯一
  if (user.isNew() || user.dirtyKeys().includes('email')) {
    const email = user.get('email');
    if (email) {
      const query = new Parse.Query(Parse.User);
      query.equalTo('email', email);
      if (!user.isNew()) {
        query.notEqualTo('objectId', user.id);
      }
      const existingUser = await query.first({ useMasterKey: true });
      if (existingUser) {
        throw new Parse.Error(Parse.Error.EMAIL_TAKEN, 'Email already exists');
      }
    }
  }
  
  // 设置默认值
  if (user.isNew()) {
    user.set('isActive', true);
    user.set('isEmailVerified', false);
    user.set('preferences', {
      theme: 'system',
      notifications: true,
      weekStartsOn: 'monday',
      timezone: 'UTC'
    });
    user.set('notificationSettings', {
      taskReminders: true,
      habitReminders: true,
      dailySummary: false,
      reminderTime: '09:00'
    });
  }
});

/**
 * 任务保存前触发器
 */
Parse.Cloud.beforeSave('Task', async (request) => {
  const task = request.object;
  const user = request.user;
  
  if (!user) {
    throw new Parse.Error(Parse.Error.SESSION_MISSING, 'User must be authenticated');
  }
  
  // 设置用户ID
  if (task.isNew()) {
    task.set('userId', user.id);
    task.set('user', user);
  }
  
  // 验证用户权限
  if (task.get('userId') !== user.id) {
    throw new Parse.Error(Parse.Error.OPERATION_FORBIDDEN, 'Access denied');
  }
  
  // 验证必填字段
  if (!task.get('title') || task.get('title').trim().length === 0) {
    throw new Parse.Error(Parse.Error.VALIDATION_ERROR, 'Title is required');
  }
  
  // 设置默认值
  if (task.isNew()) {
    task.set('isCompleted', false);
    task.set('priority', task.get('priority') || 'medium');
    task.set('status', task.get('status') || 'pending');
    task.set('isDeleted', false);
  }
  
  // 处理完成状态变更
  if (task.dirtyKeys().includes('isCompleted')) {
    if (task.get('isCompleted')) {
      task.set('completedAt', new Date());
      task.set('status', 'completed');
    } else {
      task.unset('completedAt');
      task.set('status', 'pending');
    }
  }
});

/**
 * 习惯保存前触发器
 */
Parse.Cloud.beforeSave('Habit', async (request) => {
  const habit = request.object;
  const user = request.user;
  
  if (!user) {
    throw new Parse.Error(Parse.Error.SESSION_MISSING, 'User must be authenticated');
  }
  
  // 设置用户ID
  if (habit.isNew()) {
    habit.set('userId', user.id);
    habit.set('user', user);
  }
  
  // 验证用户权限
  if (habit.get('userId') !== user.id) {
    throw new Parse.Error(Parse.Error.OPERATION_FORBIDDEN, 'Access denied');
  }
  
  // 验证必填字段
  if (!habit.get('name') || habit.get('name').trim().length === 0) {
    throw new Parse.Error(Parse.Error.VALIDATION_ERROR, 'Name is required');
  }
  
  // 设置默认值
  if (habit.isNew()) {
    habit.set('frequency', habit.get('frequency') || 'daily');
    habit.set('type', habit.get('type') || 'boolean');
    habit.set('targetValue', habit.get('targetValue') || 1);
    habit.set('isActive', true);
    habit.set('currentStreak', 0);
    habit.set('bestStreak', 0);
    habit.set('totalCompletions', 0);
    habit.set('isDeleted', false);
  }
});

/**
 * 习惯记录保存前触发器
 */
Parse.Cloud.beforeSave('HabitRecord', async (request) => {
  const record = request.object;
  const user = request.user;
  
  if (!user) {
    throw new Parse.Error(Parse.Error.SESSION_MISSING, 'User must be authenticated');
  }
  
  // 设置用户ID
  if (record.isNew()) {
    record.set('userId', user.id);
    record.set('user', user);
  }
  
  // 验证用户权限
  if (record.get('userId') !== user.id) {
    throw new Parse.Error(Parse.Error.OPERATION_FORBIDDEN, 'Access denied');
  }
  
  // 验证必填字段
  if (!record.get('habitId')) {
    throw new Parse.Error(Parse.Error.VALIDATION_ERROR, 'Habit ID is required');
  }
  
  if (!record.get('date')) {
    throw new Parse.Error(Parse.Error.VALIDATION_ERROR, 'Date is required');
  }
  
  // 验证习惯存在且属于当前用户
  const habitQuery = new Parse.Query('Habit');
  habitQuery.equalTo('objectId', record.get('habitId'));
  habitQuery.equalTo('userId', user.id);
  const habit = await habitQuery.first({ useMasterKey: true });
  
  if (!habit) {
    throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, 'Habit not found');
  }
  
  record.set('habit', habit);
  
  // 设置默认值
  if (record.isNew()) {
    record.set('value', record.get('value') || 1);
  }
});

/**
 * 习惯记录保存后触发器
 */
Parse.Cloud.afterSave('HabitRecord', async (request) => {
  const record = request.object;
  const habitId = record.get('habitId');
  
  try {
    // 更新习惯统计
    const habit = await new Parse.Query('Habit').get(habitId, { useMasterKey: true });
    
    // 获取所有记录
    const recordsQuery = new Parse.Query('HabitRecord');
    recordsQuery.equalTo('habitId', habitId);
    recordsQuery.ascending('date');
    const records = await recordsQuery.find({ useMasterKey: true });
    
    // 计算总完成次数
    const totalCompletions = records.length;
    
    // 计算连续天数
    const { currentStreak, bestStreak } = calculateStreaks(records);
    
    // 更新习惯统计
    habit.set('totalCompletions', totalCompletions);
    habit.set('currentStreak', currentStreak);
    if (bestStreak > habit.get('bestStreak')) {
      habit.set('bestStreak', bestStreak);
    }
    
    await habit.save(null, { useMasterKey: true });
    
  } catch (error) {
    console.error('Failed to update habit statistics:', error);
  }
});

/**
 * 计算连续天数
 */
function calculateStreaks(records) {
  if (records.length === 0) {
    return { currentStreak: 0, bestStreak: 0 };
  }
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  let currentStreak = 0;
  let bestStreak = 0;
  let tempStreak = 0;
  
  // 从最新记录开始计算当前连续天数
  for (let i = records.length - 1; i >= 0; i--) {
    const recordDate = new Date(records[i].get('date'));
    const expectedDate = new Date(today.getTime() - currentStreak * 24 * 60 * 60 * 1000);
    
    if (recordDate.getTime() === expectedDate.getTime()) {
      currentStreak++;
    } else {
      break;
    }
  }
  
  // 计算历史最佳连续天数
  let lastDate = null;
  for (const record of records) {
    const recordDate = new Date(record.get('date'));
    
    if (lastDate && recordDate.getTime() === lastDate.getTime() + 24 * 60 * 60 * 1000) {
      tempStreak++;
    } else {
      tempStreak = 1;
    }
    
    bestStreak = Math.max(bestStreak, tempStreak);
    lastDate = recordDate;
  }
  
  return { currentStreak, bestStreak };
}

/**
 * 云函数：获取用户统计信息
 */
Parse.Cloud.define('getUserStats', async (request) => {
  const user = request.user;
  
  if (!user) {
    throw new Parse.Error(Parse.Error.SESSION_MISSING, 'User must be authenticated');
  }
  
  const userId = user.id;
  
  // 任务统计
  const totalTasks = await new Parse.Query('Task')
    .equalTo('userId', userId)
    .notEqualTo('isDeleted', true)
    .count({ useMasterKey: true });
  
  const completedTasks = await new Parse.Query('Task')
    .equalTo('userId', userId)
    .equalTo('isCompleted', true)
    .notEqualTo('isDeleted', true)
    .count({ useMasterKey: true });
  
  // 习惯统计
  const totalHabits = await new Parse.Query('Habit')
    .equalTo('userId', userId)
    .notEqualTo('isDeleted', true)
    .count({ useMasterKey: true });
  
  const activeHabits = await new Parse.Query('Habit')
    .equalTo('userId', userId)
    .equalTo('isActive', true)
    .notEqualTo('isDeleted', true)
    .count({ useMasterKey: true });
  
  return {
    tasks: {
      total: totalTasks,
      completed: completedTasks,
      pending: totalTasks - completedTasks,
      completionRate: totalTasks > 0 ? ((completedTasks / totalTasks) * 100).toFixed(1) : 0
    },
    habits: {
      total: totalHabits,
      active: activeHabits,
      inactive: totalHabits - activeHabits
    }
  };
});

/**
 * 云函数：清理过期数据
 */
Parse.Cloud.define('cleanupExpiredData', async (request) => {
  // 只允许管理员调用
  if (!request.master) {
    throw new Parse.Error(Parse.Error.OPERATION_FORBIDDEN, 'Master key required');
  }
  
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  
  // 清理软删除的任务
  const deletedTasks = await new Parse.Query('Task')
    .equalTo('isDeleted', true)
    .lessThan('deletedAt', thirtyDaysAgo)
    .find({ useMasterKey: true });
  
  await Parse.Object.destroyAll(deletedTasks, { useMasterKey: true });
  
  // 清理软删除的习惯
  const deletedHabits = await new Parse.Query('Habit')
    .equalTo('isDeleted', true)
    .lessThan('deletedAt', thirtyDaysAgo)
    .find({ useMasterKey: true });
  
  await Parse.Object.destroyAll(deletedHabits, { useMasterKey: true });
  
  // 清理过期会话
  const expiredSessions = await new Parse.Query(Parse.Session)
    .lessThan('expiresAt', new Date())
    .find({ useMasterKey: true });
  
  await Parse.Object.destroyAll(expiredSessions, { useMasterKey: true });
  
  return {
    deletedTasks: deletedTasks.length,
    deletedHabits: deletedHabits.length,
    expiredSessions: expiredSessions.length
  };
});

/**
 * 云函数：发送推送通知
 */
Parse.Cloud.define('sendPushNotification', async (request) => {
  const { userId, title, message, data } = request.params;
  
  if (!request.master && (!request.user || request.user.id !== userId)) {
    throw new Parse.Error(Parse.Error.OPERATION_FORBIDDEN, 'Access denied');
  }
  
  const userQuery = new Parse.Query(Parse.User);
  userQuery.equalTo('objectId', userId);
  
  const pushQuery = new Parse.Query(Parse.Installation);
  pushQuery.matchesQuery('user', userQuery);
  
  await Parse.Push.send({
    where: pushQuery,
    data: {
      alert: message,
      title: title,
      sound: 'default',
      badge: 'Increment',
      ...data
    }
  }, { useMasterKey: true });
  
  return { success: true };
});

module.exports = Parse.Cloud;
