/**
 * 数据同步路由
 * 处理客户端与服务器之间的数据同步
 */

const express = require('express');
const Parse = require('parse/node');
const { requireAuth } = require('../middleware');
const { body, query } = require('express-validator');
const { logApiCall, logUserAction } = require('../utils/logger');

const router = express.Router();

// 验证规则
const syncUploadValidation = [
  body('changes')
    .isArray()
    .withMessage('Changes must be an array'),
  body('changes.*.type')
    .isIn(['task', 'habit', 'habitRecord'])
    .withMessage('Invalid change type'),
  body('changes.*.action')
    .isIn(['create', 'update', 'delete'])
    .withMessage('Invalid action'),
  body('changes.*.data')
    .isObject()
    .withMessage('Data must be an object'),
  body('changes.*.clientId')
    .notEmpty()
    .withMessage('Client ID is required'),
  body('changes.*.timestamp')
    .isISO8601()
    .withMessage('Timestamp must be a valid ISO 8601 date')
];

// 中间件：验证用户认证
router.use(requireAuth);

/**
 * GET /api/v1/sync
 * 获取增量同步数据
 */
router.get('/', [
  query('lastSyncTime')
    .optional()
    .isISO8601()
    .withMessage('lastSyncTime must be a valid ISO 8601 date')
], async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const lastSyncTime = req.query.lastSyncTime ? new Date(req.query.lastSyncTime) : new Date(0);
    const currentTime = new Date();
    
    // 获取任务变更
    const taskChanges = await getTaskChanges(userId, lastSyncTime);
    
    // 获取习惯变更
    const habitChanges = await getHabitChanges(userId, lastSyncTime);
    
    // 获取习惯记录变更
    const habitRecordChanges = await getHabitRecordChanges(userId, lastSyncTime);
    
    const syncData = {
      tasks: taskChanges,
      habits: habitChanges,
      habitRecords: habitRecordChanges,
      syncTime: currentTime.toISOString()
    };
    
    // 记录用户操作
    logUserAction(userId, 'SYNC_DOWNLOAD', {
      lastSyncTime: lastSyncTime.toISOString(),
      changesCount: {
        tasks: taskChanges.created.length + taskChanges.updated.length + taskChanges.deleted.length,
        habits: habitChanges.created.length + habitChanges.updated.length + habitChanges.deleted.length,
        habitRecords: habitRecordChanges.created.length + habitRecordChanges.updated.length + habitRecordChanges.deleted.length
      }
    });
    
    logApiCall('/api/v1/sync', 'GET', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: syncData,
      message: 'Sync data retrieved successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/sync', 'GET', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * POST /api/v1/sync
 * 上传本地变更
 */
router.post('/', syncUploadValidation, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const { changes } = req.body;
    
    const results = [];
    const conflicts = [];
    
    for (const change of changes) {
      try {
        const result = await processChange(userId, change);
        results.push({
          clientId: change.clientId,
          success: true,
          serverId: result.id,
          serverTimestamp: result.updatedAt
        });
      } catch (error) {
        if (error.code === 'CONFLICT') {
          conflicts.push({
            clientId: change.clientId,
            conflict: error.conflict,
            serverData: error.serverData
          });
        } else {
          results.push({
            clientId: change.clientId,
            success: false,
            error: error.message
          });
        }
      }
    }
    
    // 记录用户操作
    logUserAction(userId, 'SYNC_UPLOAD', {
      changesCount: changes.length,
      successCount: results.filter(r => r.success).length,
      conflictCount: conflicts.length
    });
    
    logApiCall('/api/v1/sync', 'POST', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: {
        results,
        conflicts,
        syncTime: new Date().toISOString()
      },
      message: 'Changes uploaded successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/sync', 'POST', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * 获取任务变更
 */
async function getTaskChanges(userId, lastSyncTime) {
  const query = new Parse.Query('Task')
    .equalTo('userId', userId)
    .greaterThan('updatedAt', lastSyncTime);
  
  const tasks = await query.find();
  
  const created = [];
  const updated = [];
  const deleted = [];
  
  tasks.forEach(task => {
    const taskData = {
      id: task.id,
      title: task.get('title'),
      description: task.get('description'),
      isCompleted: task.get('isCompleted'),
      priority: task.get('priority'),
      status: task.get('status'),
      dueDate: task.get('dueDate'),
      category: task.get('category'),
      tags: task.get('tags'),
      estimatedDuration: task.get('estimatedDuration'),
      actualDuration: task.get('actualDuration'),
      completedAt: task.get('completedAt'),
      createdAt: task.get('createdAt'),
      updatedAt: task.get('updatedAt')
    };
    
    if (task.get('isDeleted')) {
      deleted.push(task.id);
    } else if (task.get('createdAt') > lastSyncTime) {
      created.push(taskData);
    } else {
      updated.push(taskData);
    }
  });
  
  return { created, updated, deleted };
}

/**
 * 获取习惯变更
 */
async function getHabitChanges(userId, lastSyncTime) {
  const query = new Parse.Query('Habit')
    .equalTo('userId', userId)
    .greaterThan('updatedAt', lastSyncTime);
  
  const habits = await query.find();
  
  const created = [];
  const updated = [];
  const deleted = [];
  
  habits.forEach(habit => {
    const habitData = {
      id: habit.id,
      name: habit.get('name'),
      description: habit.get('description'),
      frequency: habit.get('frequency'),
      type: habit.get('type'),
      targetValue: habit.get('targetValue'),
      unit: habit.get('unit'),
      icon: habit.get('icon'),
      color: habit.get('color'),
      isActive: habit.get('isActive'),
      startDate: habit.get('startDate'),
      endDate: habit.get('endDate'),
      reminderTime: habit.get('reminderTime'),
      isReminderEnabled: habit.get('isReminderEnabled'),
      currentStreak: habit.get('currentStreak'),
      bestStreak: habit.get('bestStreak'),
      totalCompletions: habit.get('totalCompletions'),
      createdAt: habit.get('createdAt'),
      updatedAt: habit.get('updatedAt')
    };
    
    if (habit.get('isDeleted')) {
      deleted.push(habit.id);
    } else if (habit.get('createdAt') > lastSyncTime) {
      created.push(habitData);
    } else {
      updated.push(habitData);
    }
  });
  
  return { created, updated, deleted };
}

/**
 * 获取习惯记录变更
 */
async function getHabitRecordChanges(userId, lastSyncTime) {
  const query = new Parse.Query('HabitRecord')
    .equalTo('userId', userId)
    .greaterThan('updatedAt', lastSyncTime);
  
  const records = await query.find();
  
  const created = [];
  const updated = [];
  const deleted = [];
  
  records.forEach(record => {
    const recordData = {
      id: record.id,
      habitId: record.get('habitId'),
      date: record.get('date'),
      value: record.get('value'),
      note: record.get('note'),
      duration: record.get('duration'),
      createdAt: record.get('createdAt'),
      updatedAt: record.get('updatedAt')
    };
    
    if (record.get('createdAt') > lastSyncTime) {
      created.push(recordData);
    } else {
      updated.push(recordData);
    }
  });
  
  return { created, updated, deleted };
}

/**
 * 处理单个变更
 */
async function processChange(userId, change) {
  const { type, action, data, clientId, timestamp } = change;
  
  let ParseClass;
  switch (type) {
    case 'task':
      ParseClass = Parse.Object.extend('Task');
      break;
    case 'habit':
      ParseClass = Parse.Object.extend('Habit');
      break;
    case 'habitRecord':
      ParseClass = Parse.Object.extend('HabitRecord');
      break;
    default:
      throw new Error(`Invalid type: ${type}`);
  }
  
  if (action === 'create') {
    const object = new ParseClass();
    object.set('userId', userId);
    
    Object.keys(data).forEach(key => {
      if (key !== 'id' && data[key] !== undefined) {
        object.set(key, data[key]);
      }
    });
    
    return await object.save();
    
  } else if (action === 'update') {
    const query = new Parse.Query(ParseClass)
      .equalTo('userId', userId)
      .equalTo('objectId', data.id);
    
    const object = await query.first();
    
    if (!object) {
      throw new Error(`${type} not found`);
    }
    
    // 检查冲突
    const serverTimestamp = new Date(object.get('updatedAt'));
    const clientTimestamp = new Date(timestamp);
    
    if (serverTimestamp > clientTimestamp) {
      const error = new Error('Conflict detected');
      error.code = 'CONFLICT';
      error.conflict = {
        type: 'update_conflict',
        serverTimestamp: serverTimestamp.toISOString(),
        clientTimestamp: clientTimestamp.toISOString()
      };
      error.serverData = object.toJSON();
      throw error;
    }
    
    Object.keys(data).forEach(key => {
      if (key !== 'id' && data[key] !== undefined) {
        object.set(key, data[key]);
      }
    });
    
    return await object.save();
    
  } else if (action === 'delete') {
    const query = new Parse.Query(ParseClass)
      .equalTo('userId', userId)
      .equalTo('objectId', data.id);
    
    const object = await query.first();
    
    if (!object) {
      throw new Error(`${type} not found`);
    }
    
    // 软删除
    object.set('isDeleted', true);
    object.set('deletedAt', new Date());
    
    return await object.save();
  }
}

module.exports = router;
