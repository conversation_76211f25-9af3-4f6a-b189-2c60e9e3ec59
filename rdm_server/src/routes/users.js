/**
 * 用户路由
 * 处理用户管理相关的API端点
 */

const express = require('express');
const Parse = require('parse/node');
const { requireAuth } = require('../middleware');
const { body } = require('express-validator');
const { logApiCall, logUserAction } = require('../utils/logger');

const router = express.Router();

// 验证规则
const updateProfileValidation = [
  body('username')
    .optional()
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  body('fullName')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Full name must be less than 100 characters'),
  body('timezone')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Timezone must be less than 50 characters'),
  body('preferences')
    .optional()
    .isObject()
    .withMessage('Preferences must be an object')
];

const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number')
];

const updateNotificationValidation = [
  body('taskReminders')
    .optional()
    .isBoolean()
    .withMessage('taskReminders must be a boolean'),
  body('habitReminders')
    .optional()
    .isBoolean()
    .withMessage('habitReminders must be a boolean'),
  body('dailySummary')
    .optional()
    .isBoolean()
    .withMessage('dailySummary must be a boolean'),
  body('reminderTime')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('reminderTime must be in HH:MM format')
];

// 中间件：验证用户认证
router.use(requireAuth);

/**
 * GET /api/v1/users/me
 * 获取当前用户信息
 */
router.get('/me', async (req, res) => {
  const startTime = Date.now();
  
  try {
    const user = req.user;
    
    const userData = {
      id: user.id,
      username: user.get('username'),
      email: user.get('email'),
      fullName: user.get('fullName'),
      isEmailVerified: user.get('isEmailVerified'),
      isActive: user.get('isActive'),
      authProvider: user.get('authProvider'),
      timezone: user.get('timezone'),
      preferences: user.get('preferences') || {},
      notificationSettings: user.get('notificationSettings') || {},
      lastLoginAt: user.get('lastLoginAt'),
      createdAt: user.get('createdAt'),
      updatedAt: user.get('updatedAt')
    };
    
    logApiCall('/api/v1/users/me', 'GET', 200, Date.now() - startTime, user.id);
    
    res.json({
      success: true,
      data: userData,
      message: 'User profile retrieved successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/users/me', 'GET', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * PUT /api/v1/users/me
 * 更新用户信息
 */
router.put('/me', updateProfileValidation, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const user = req.user;
    const updateData = req.body;
    
    // 检查用户名是否已被其他用户使用
    if (updateData.username && updateData.username !== user.get('username')) {
      const existingUser = await new Parse.Query(Parse.User)
        .equalTo('username', updateData.username)
        .notEqualTo('objectId', user.id)
        .first();
      
      if (existingUser) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'USERNAME_EXISTS',
            message: 'Username already exists'
          }
        });
      }
    }
    
    // 检查邮箱是否已被其他用户使用
    if (updateData.email && updateData.email !== user.get('email')) {
      const existingEmail = await new Parse.Query(Parse.User)
        .equalTo('email', updateData.email)
        .notEqualTo('objectId', user.id)
        .first();
      
      if (existingEmail) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'EMAIL_EXISTS',
            message: 'Email already exists'
          }
        });
      }
      
      // 如果更改邮箱，需要重新验证
      user.set('isEmailVerified', false);
    }
    
    // 更新允许的字段
    const allowedFields = ['username', 'email', 'fullName', 'timezone', 'preferences'];
    
    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key) && updateData[key] !== undefined) {
        user.set(key, updateData[key]);
      }
    });
    
    const updatedUser = await user.save();
    
    // 记录用户操作
    logUserAction(user.id, 'UPDATE_PROFILE', {
      changes: Object.keys(updateData),
      ip: req.ip
    });
    
    logApiCall('/api/v1/users/me', 'PUT', 200, Date.now() - startTime, user.id);
    
    res.json({
      success: true,
      data: {
        id: updatedUser.id,
        username: updatedUser.get('username'),
        email: updatedUser.get('email'),
        fullName: updatedUser.get('fullName'),
        isEmailVerified: updatedUser.get('isEmailVerified'),
        timezone: updatedUser.get('timezone'),
        preferences: updatedUser.get('preferences'),
        updatedAt: updatedUser.get('updatedAt')
      },
      message: 'Profile updated successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/users/me', 'PUT', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * POST /api/v1/users/change-password
 * 修改密码
 */
router.post('/change-password', changePasswordValidation, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const user = req.user;
    const { currentPassword, newPassword } = req.body;
    
    // 验证当前密码
    try {
      await Parse.User.logIn(user.get('username'), currentPassword);
    } catch (error) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_CURRENT_PASSWORD',
          message: 'Current password is incorrect'
        }
      });
    }
    
    // 更新密码
    user.set('password', newPassword);
    await user.save();
    
    // 记录用户操作
    logUserAction(user.id, 'CHANGE_PASSWORD', {
      ip: req.ip
    });
    
    logApiCall('/api/v1/users/change-password', 'POST', 200, Date.now() - startTime, user.id);
    
    res.json({
      success: true,
      message: 'Password changed successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/users/change-password', 'POST', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * PUT /api/v1/users/me/notifications
 * 更新通知设置
 */
router.put('/me/notifications', updateNotificationValidation, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const user = req.user;
    const notificationSettings = req.body;
    
    // 获取当前通知设置
    const currentSettings = user.get('notificationSettings') || {};
    
    // 合并新设置
    const updatedSettings = {
      ...currentSettings,
      ...notificationSettings
    };
    
    user.set('notificationSettings', updatedSettings);
    await user.save();
    
    // 记录用户操作
    logUserAction(user.id, 'UPDATE_NOTIFICATIONS', {
      changes: Object.keys(notificationSettings),
      ip: req.ip
    });
    
    logApiCall('/api/v1/users/me/notifications', 'PUT', 200, Date.now() - startTime, user.id);
    
    res.json({
      success: true,
      data: updatedSettings,
      message: 'Notification settings updated successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/users/me/notifications', 'PUT', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * DELETE /api/v1/users/me
 * 删除用户账户
 */
router.delete('/me', async (req, res) => {
  const startTime = Date.now();
  
  try {
    const user = req.user;
    const userId = user.id;
    
    // 软删除用户（标记为非活跃）
    user.set('isActive', false);
    user.set('deletedAt', new Date());
    await user.save();
    
    // 记录用户操作
    logUserAction(userId, 'DELETE_ACCOUNT', {
      ip: req.ip
    });
    
    logApiCall('/api/v1/users/me', 'DELETE', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      message: 'Account deleted successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/users/me', 'DELETE', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * GET /api/v1/users/me/stats
 * 获取用户统计信息
 */
router.get('/me/stats', async (req, res) => {
  const startTime = Date.now();
  
  try {
    const user = req.user;
    const userId = user.id;
    
    // 获取任务统计
    const totalTasks = await new Parse.Query('Task')
      .equalTo('userId', userId)
      .notEqualTo('isDeleted', true)
      .count();
    
    const completedTasks = await new Parse.Query('Task')
      .equalTo('userId', userId)
      .equalTo('isCompleted', true)
      .notEqualTo('isDeleted', true)
      .count();
    
    // 获取习惯统计
    const totalHabits = await new Parse.Query('Habit')
      .equalTo('userId', userId)
      .notEqualTo('isDeleted', true)
      .count();
    
    const activeHabits = await new Parse.Query('Habit')
      .equalTo('userId', userId)
      .equalTo('isActive', true)
      .notEqualTo('isDeleted', true)
      .count();
    
    // 获取习惯记录统计
    const totalHabitRecords = await new Parse.Query('HabitRecord')
      .equalTo('userId', userId)
      .count();
    
    const stats = {
      tasks: {
        total: totalTasks,
        completed: completedTasks,
        pending: totalTasks - completedTasks,
        completionRate: totalTasks > 0 ? ((completedTasks / totalTasks) * 100).toFixed(1) : 0
      },
      habits: {
        total: totalHabits,
        active: activeHabits,
        inactive: totalHabits - activeHabits,
        totalRecords: totalHabitRecords
      },
      account: {
        memberSince: user.get('createdAt'),
        lastLogin: user.get('lastLoginAt'),
        isEmailVerified: user.get('isEmailVerified')
      }
    };
    
    logApiCall('/api/v1/users/me/stats', 'GET', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: stats,
      message: 'User statistics retrieved successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/users/me/stats', 'GET', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

module.exports = router;
