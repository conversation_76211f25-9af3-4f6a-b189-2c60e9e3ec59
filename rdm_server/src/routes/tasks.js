/**
 * 任务路由
 * 处理任务相关的API端点
 */

const express = require('express');
const taskService = require('../services/taskService');
const { requireAuth, validateInput } = require('../middleware');
const { body, query, param } = require('express-validator');
const { logApiCall } = require('../utils/logger');

const router = express.Router();

// 验证规则
const createTaskValidation = [
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ max: 200 })
    .withMessage('Title must be less than 200 characters'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be less than 1000 characters'),
  body('priority')
    .optional()
    .isIn(['high', 'medium', 'low'])
    .withMessage('Priority must be high, medium, or low'),
  body('dueDate')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid ISO 8601 date'),
  body('category')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Category must be less than 50 characters'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('estimatedDuration')
    .optional()
    .isNumeric()
    .withMessage('Estimated duration must be a number')
];

const updateTaskValidation = [
  body('title')
    .optional()
    .notEmpty()
    .withMessage('Title cannot be empty')
    .isLength({ max: 200 })
    .withMessage('Title must be less than 200 characters'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be less than 1000 characters'),
  body('priority')
    .optional()
    .isIn(['high', 'medium', 'low'])
    .withMessage('Priority must be high, medium, or low'),
  body('status')
    .optional()
    .isIn(['pending', 'in_progress', 'completed', 'cancelled'])
    .withMessage('Invalid status value'),
  body('isCompleted')
    .optional()
    .isBoolean()
    .withMessage('isCompleted must be a boolean'),
  body('dueDate')
    .optional()
    .isISO8601()
    .withMessage('Due date must be a valid ISO 8601 date')
];

const batchUpdateValidation = [
  body('taskIds')
    .isArray({ min: 1 })
    .withMessage('taskIds must be a non-empty array'),
  body('operation')
    .isIn(['complete', 'incomplete', 'delete', 'update'])
    .withMessage('Invalid operation'),
  body('data')
    .optional()
    .isObject()
    .withMessage('data must be an object')
];

// 中间件：验证用户认证
router.use(requireAuth);

/**
 * GET /api/v1/tasks
 * 获取用户的任务列表
 */
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['pending', 'in_progress', 'completed', 'cancelled']),
  query('priority').optional().isIn(['high', 'medium', 'low']),
  query('isCompleted').optional().isBoolean(),
  query('sortBy').optional().isIn(['createdAt', 'updatedAt', 'dueDate', 'priority']),
  query('sortOrder').optional().isIn(['asc', 'desc'])
], async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const options = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 20,
      status: req.query.status,
      priority: req.query.priority,
      isCompleted: req.query.isCompleted === 'true' ? true : req.query.isCompleted === 'false' ? false : undefined,
      category: req.query.category,
      dueDateStart: req.query.dueDateStart,
      dueDateEnd: req.query.dueDateEnd,
      search: req.query.search,
      sortBy: req.query.sortBy || 'createdAt',
      sortOrder: req.query.sortOrder || 'desc'
    };
    
    const result = await taskService.getUserTasks(userId, options);
    
    logApiCall('/api/v1/tasks', 'GET', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: result,
      message: 'Tasks retrieved successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/tasks', 'GET', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * POST /api/v1/tasks
 * 创建新任务
 */
router.post('/', createTaskValidation, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const taskData = req.body;
    
    const task = await taskService.createTask(userId, taskData);
    
    logApiCall('/api/v1/tasks', 'POST', 201, Date.now() - startTime, userId);
    
    res.status(201).json({
      success: true,
      data: task,
      message: 'Task created successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/tasks', 'POST', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * GET /api/v1/tasks/:id
 * 获取单个任务
 */
router.get('/:id', [
  param('id').notEmpty().withMessage('Task ID is required')
], async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const taskId = req.params.id;
    
    const task = await taskService.getTaskById(userId, taskId);
    
    logApiCall(`/api/v1/tasks/${taskId}`, 'GET', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: task,
      message: 'Task retrieved successfully'
    });
    
  } catch (error) {
    logApiCall(`/api/v1/tasks/${req.params.id}`, 'GET', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * PUT /api/v1/tasks/:id
 * 更新任务
 */
router.put('/:id', [
  param('id').notEmpty().withMessage('Task ID is required'),
  ...updateTaskValidation
], async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const taskId = req.params.id;
    const updateData = req.body;
    
    const task = await taskService.updateTask(userId, taskId, updateData);
    
    logApiCall(`/api/v1/tasks/${taskId}`, 'PUT', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: task,
      message: 'Task updated successfully'
    });
    
  } catch (error) {
    logApiCall(`/api/v1/tasks/${req.params.id}`, 'PUT', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * DELETE /api/v1/tasks/:id
 * 删除任务
 */
router.delete('/:id', [
  param('id').notEmpty().withMessage('Task ID is required')
], async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const taskId = req.params.id;
    
    const result = await taskService.deleteTask(userId, taskId);
    
    logApiCall(`/api/v1/tasks/${taskId}`, 'DELETE', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: result,
      message: 'Task deleted successfully'
    });
    
  } catch (error) {
    logApiCall(`/api/v1/tasks/${req.params.id}`, 'DELETE', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * POST /api/v1/tasks/batch
 * 批量操作任务
 */
router.post('/batch', batchUpdateValidation, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const { taskIds, operation, data } = req.body;
    
    const result = await taskService.batchUpdateTasks(userId, taskIds, operation, data);
    
    logApiCall('/api/v1/tasks/batch', 'POST', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: result,
      message: 'Batch operation completed successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/tasks/batch', 'POST', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * GET /api/v1/tasks/statistics
 * 获取任务统计信息
 */
router.get('/statistics', [
  query('period').optional().isIn(['day', 'week', 'month']).withMessage('Period must be day, week, or month')
], async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const period = req.query.period || 'week';
    
    const statistics = await taskService.getTaskStatistics(userId, period);
    
    logApiCall('/api/v1/tasks/statistics', 'GET', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: statistics,
      message: 'Task statistics retrieved successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/tasks/statistics', 'GET', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

module.exports = router;
