/**
 * 习惯路由
 * 处理习惯追踪相关的API端点
 */

const express = require('express');
const habitService = require('../services/habitService');
const { requireAuth } = require('../middleware');
const { body, query, param } = require('express-validator');
const { logApiCall } = require('../utils/logger');

const router = express.Router();

// 验证规则
const createHabitValidation = [
  body('name')
    .notEmpty()
    .withMessage('Name is required')
    .isLength({ max: 100 })
    .withMessage('Name must be less than 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('frequency')
    .optional()
    .isIn(['daily', 'weekly', 'monthly', 'custom'])
    .withMessage('Frequency must be daily, weekly, monthly, or custom'),
  body('type')
    .optional()
    .isIn(['boolean', 'numeric', 'duration'])
    .withMessage('Type must be boolean, numeric, or duration'),
  body('targetValue')
    .optional()
    .isNumeric()
    .withMessage('Target value must be a number'),
  body('unit')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Unit must be less than 20 characters'),
  body('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  body('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date')
];

const updateHabitValidation = [
  body('name')
    .optional()
    .notEmpty()
    .withMessage('Name cannot be empty')
    .isLength({ max: 100 })
    .withMessage('Name must be less than 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('frequency')
    .optional()
    .isIn(['daily', 'weekly', 'monthly', 'custom'])
    .withMessage('Frequency must be daily, weekly, monthly, or custom'),
  body('type')
    .optional()
    .isIn(['boolean', 'numeric', 'duration'])
    .withMessage('Type must be boolean, numeric, or duration'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
];

const recordHabitValidation = [
  body('date')
    .optional()
    .isISO8601()
    .withMessage('Date must be a valid ISO 8601 date'),
  body('value')
    .optional()
    .isNumeric()
    .withMessage('Value must be a number'),
  body('note')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Note must be less than 200 characters'),
  body('duration')
    .optional()
    .isNumeric()
    .withMessage('Duration must be a number')
];

// 中间件：验证用户认证
router.use(requireAuth);

/**
 * GET /api/v1/habits
 * 获取用户的习惯列表
 */
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('frequency').optional().isIn(['daily', 'weekly', 'monthly', 'custom']),
  query('isActive').optional().isBoolean(),
  query('sortBy').optional().isIn(['createdAt', 'updatedAt', 'name']),
  query('sortOrder').optional().isIn(['asc', 'desc'])
], async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const options = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 20,
      frequency: req.query.frequency,
      isActive: req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined,
      sortBy: req.query.sortBy || 'createdAt',
      sortOrder: req.query.sortOrder || 'desc'
    };
    
    const result = await habitService.getUserHabits(userId, options);
    
    logApiCall('/api/v1/habits', 'GET', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: result,
      message: 'Habits retrieved successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/habits', 'GET', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * POST /api/v1/habits
 * 创建新习惯
 */
router.post('/', createHabitValidation, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const habitData = req.body;
    
    const habit = await habitService.createHabit(userId, habitData);
    
    logApiCall('/api/v1/habits', 'POST', 201, Date.now() - startTime, userId);
    
    res.status(201).json({
      success: true,
      data: habit,
      message: 'Habit created successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/habits', 'POST', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * GET /api/v1/habits/:id
 * 获取单个习惯
 */
router.get('/:id', [
  param('id').notEmpty().withMessage('Habit ID is required')
], async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const habitId = req.params.id;
    
    const habit = await habitService.getHabitById(userId, habitId);
    
    logApiCall(`/api/v1/habits/${habitId}`, 'GET', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: habit,
      message: 'Habit retrieved successfully'
    });
    
  } catch (error) {
    logApiCall(`/api/v1/habits/${req.params.id}`, 'GET', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * PUT /api/v1/habits/:id
 * 更新习惯
 */
router.put('/:id', [
  param('id').notEmpty().withMessage('Habit ID is required'),
  ...updateHabitValidation
], async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const habitId = req.params.id;
    const updateData = req.body;
    
    const habit = await habitService.updateHabit(userId, habitId, updateData);
    
    logApiCall(`/api/v1/habits/${habitId}`, 'PUT', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: habit,
      message: 'Habit updated successfully'
    });
    
  } catch (error) {
    logApiCall(`/api/v1/habits/${req.params.id}`, 'PUT', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * DELETE /api/v1/habits/:id
 * 删除习惯
 */
router.delete('/:id', [
  param('id').notEmpty().withMessage('Habit ID is required')
], async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const habitId = req.params.id;
    
    const result = await habitService.deleteHabit(userId, habitId);
    
    logApiCall(`/api/v1/habits/${habitId}`, 'DELETE', 200, Date.now() - startTime, userId);
    
    res.json({
      success: true,
      data: result,
      message: 'Habit deleted successfully'
    });
    
  } catch (error) {
    logApiCall(`/api/v1/habits/${req.params.id}`, 'DELETE', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

/**
 * POST /api/v1/habits/:id/records
 * 记录习惯完成
 */
router.post('/:id/records', [
  param('id').notEmpty().withMessage('Habit ID is required'),
  ...recordHabitValidation
], async (req, res) => {
  const startTime = Date.now();
  
  try {
    const userId = req.user.id;
    const habitId = req.params.id;
    const recordData = req.body;
    
    const record = await habitService.recordHabitCompletion(userId, habitId, recordData);
    
    logApiCall(`/api/v1/habits/${habitId}/records`, 'POST', 201, Date.now() - startTime, userId);
    
    res.status(201).json({
      success: true,
      data: record,
      message: 'Habit completion recorded successfully'
    });
    
  } catch (error) {
    logApiCall(`/api/v1/habits/${req.params.id}/records`, 'POST', error.code || 500, Date.now() - startTime, req.user?.id);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'INTERNAL_ERROR',
        message: error.message
      }
    });
  }
});

module.exports = router;
