/**
 * 认证路由
 * 处理用户认证相关的API端点
 */

const express = require('express');
const Parse = require('parse/node');
const { body } = require('express-validator');
const { logApiCall, logUserAction, logSecurityEvent } = require('../utils/logger');
const { strictRateLimit } = require('../middleware');

const router = express.Router();

// 验证规则
const registerValidation = [
  body('email')
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  body('username')
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores')
];

const loginValidation = [
  body('email')
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

const forgotPasswordValidation = [
  body('email')
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail()
];

const resetPasswordValidation = [
  body('token')
    .notEmpty()
    .withMessage('Reset token is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number')
];

// 应用严格的速率限制到认证端点
router.use(strictRateLimit);

/**
 * POST /api/v1/auth/register
 * 用户注册
 */
router.post('/register', registerValidation, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { email, password, username } = req.body;
    
    // 检查用户名是否已存在
    const existingUser = await new Parse.Query(Parse.User)
      .equalTo('username', username)
      .first();
    
    if (existingUser) {
      logSecurityEvent('REGISTRATION_FAILED', {
        reason: 'username_exists',
        username,
        ip: req.ip
      });
      
      return res.status(400).json({
        success: false,
        error: {
          code: 'USERNAME_EXISTS',
          message: 'Username already exists'
        }
      });
    }
    
    // 检查邮箱是否已存在
    const existingEmail = await new Parse.Query(Parse.User)
      .equalTo('email', email)
      .first();
    
    if (existingEmail) {
      logSecurityEvent('REGISTRATION_FAILED', {
        reason: 'email_exists',
        email,
        ip: req.ip
      });
      
      return res.status(400).json({
        success: false,
        error: {
          code: 'EMAIL_EXISTS',
          message: 'Email already exists'
        }
      });
    }
    
    // 创建新用户
    const user = new Parse.User();
    user.set('username', username);
    user.set('email', email);
    user.set('password', password);
    
    // 设置默认用户属性
    user.set('isEmailVerified', false);
    user.set('isActive', true);
    user.set('preferences', {
      theme: 'system',
      notifications: true,
      weekStartsOn: 'monday',
      timezone: 'UTC'
    });
    
    const savedUser = await user.signUp();
    
    // 记录用户操作
    logUserAction(savedUser.id, 'REGISTER', {
      email,
      username,
      ip: req.ip
    });
    
    logApiCall('/api/v1/auth/register', 'POST', 201, Date.now() - startTime);
    
    res.status(201).json({
      success: true,
      data: {
        user: {
          id: savedUser.id,
          username: savedUser.get('username'),
          email: savedUser.get('email'),
          isEmailVerified: savedUser.get('isEmailVerified'),
          createdAt: savedUser.get('createdAt')
        },
        sessionToken: savedUser.getSessionToken()
      },
      message: 'User registered successfully'
    });
    
  } catch (error) {
    logApiCall('/api/v1/auth/register', 'POST', error.code || 500, Date.now() - startTime);
    
    logSecurityEvent('REGISTRATION_FAILED', {
      reason: 'server_error',
      error: error.message,
      ip: req.ip
    });
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'REGISTRATION_FAILED',
        message: error.message
      }
    });
  }
});

/**
 * POST /api/v1/auth/login
 * 用户登录
 */
router.post('/login', loginValidation, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { email, password } = req.body;
    
    // 使用邮箱登录
    const user = await Parse.User.logIn(email, password);
    
    // 检查用户是否被禁用
    if (!user.get('isActive')) {
      logSecurityEvent('LOGIN_FAILED', {
        reason: 'account_disabled',
        email,
        ip: req.ip
      });
      
      return res.status(403).json({
        success: false,
        error: {
          code: 'ACCOUNT_DISABLED',
          message: 'Account has been disabled'
        }
      });
    }
    
    // 更新最后登录时间
    user.set('lastLoginAt', new Date());
    user.set('lastLoginIP', req.ip);
    await user.save(null, { useMasterKey: true });
    
    // 记录用户操作
    logUserAction(user.id, 'LOGIN', {
      email,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    logApiCall('/api/v1/auth/login', 'POST', 200, Date.now() - startTime, user.id);
    
    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.get('username'),
          email: user.get('email'),
          isEmailVerified: user.get('isEmailVerified'),
          preferences: user.get('preferences'),
          lastLoginAt: user.get('lastLoginAt')
        },
        sessionToken: user.getSessionToken()
      },
      message: 'Login successful'
    });
    
  } catch (error) {
    logApiCall('/api/v1/auth/login', 'POST', error.code || 500, Date.now() - startTime);
    
    logSecurityEvent('LOGIN_FAILED', {
      reason: 'invalid_credentials',
      email: req.body.email,
      ip: req.ip,
      error: error.message
    });
    
    res.status(401).json({
      success: false,
      error: {
        code: 'INVALID_CREDENTIALS',
        message: 'Invalid email or password'
      }
    });
  }
});

/**
 * POST /api/v1/auth/apple
 * Apple ID 登录
 */
router.post('/apple', [
  body('identityToken').notEmpty().withMessage('Identity token is required'),
  body('authorizationCode').notEmpty().withMessage('Authorization code is required')
], async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { identityToken, authorizationCode, user: appleUser } = req.body;
    
    // 这里应该验证 Apple ID token
    // 为了简化，我们假设 token 是有效的
    
    const appleId = `apple_${identityToken.substring(0, 10)}`;
    
    // 查找现有用户
    let user = await new Parse.Query(Parse.User)
      .equalTo('appleId', appleId)
      .first();
    
    if (!user) {
      // 创建新用户
      user = new Parse.User();
      user.set('username', appleId);
      user.set('appleId', appleId);
      
      if (appleUser?.email) {
        user.set('email', appleUser.email);
      }
      
      if (appleUser?.name) {
        user.set('fullName', `${appleUser.name.firstName} ${appleUser.name.lastName}`);
      }
      
      user.set('isEmailVerified', true); // Apple ID 邮箱默认已验证
      user.set('isActive', true);
      user.set('authProvider', 'apple');
      
      await user.signUp();
      
      logUserAction(user.id, 'REGISTER_APPLE', {
        appleId,
        ip: req.ip
      });
    } else {
      // 更新现有用户的登录信息
      user.set('lastLoginAt', new Date());
      user.set('lastLoginIP', req.ip);
      await user.save(null, { useMasterKey: true });
      
      logUserAction(user.id, 'LOGIN_APPLE', {
        appleId,
        ip: req.ip
      });
    }
    
    // 生成会话
    await user.logIn();
    
    logApiCall('/api/v1/auth/apple', 'POST', 200, Date.now() - startTime, user.id);
    
    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.get('username'),
          email: user.get('email'),
          fullName: user.get('fullName'),
          isEmailVerified: user.get('isEmailVerified'),
          authProvider: user.get('authProvider')
        },
        sessionToken: user.getSessionToken()
      },
      message: 'Apple ID login successful'
    });
    
  } catch (error) {
    logApiCall('/api/v1/auth/apple', 'POST', error.code || 500, Date.now() - startTime);
    
    logSecurityEvent('APPLE_LOGIN_FAILED', {
      error: error.message,
      ip: req.ip
    });
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'APPLE_LOGIN_FAILED',
        message: error.message
      }
    });
  }
});

/**
 * POST /api/v1/auth/logout
 * 用户登出
 */
router.post('/logout', async (req, res) => {
  const startTime = Date.now();
  
  try {
    const sessionToken = req.headers.authorization?.replace('Bearer ', '');
    
    if (sessionToken) {
      // 查找并删除会话
      const session = await new Parse.Query(Parse.Session)
        .equalTo('sessionToken', sessionToken)
        .first({ useMasterKey: true });
      
      if (session) {
        const userId = session.get('user').id;
        await session.destroy({ useMasterKey: true });
        
        logUserAction(userId, 'LOGOUT', {
          ip: req.ip
        });
      }
    }
    
    logApiCall('/api/v1/auth/logout', 'POST', 200, Date.now() - startTime);
    
    res.json({
      success: true,
      message: 'Logout successful'
    });
    
  } catch (error) {
    logApiCall('/api/v1/auth/logout', 'POST', error.code || 500, Date.now() - startTime);
    
    res.status(error.code || 500).json({
      success: false,
      error: {
        code: error.error || 'LOGOUT_FAILED',
        message: error.message
      }
    });
  }
});

/**
 * POST /api/v1/auth/forgot-password
 * 忘记密码
 */
router.post('/forgot-password', forgotPasswordValidation, async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { email } = req.body;
    
    await Parse.User.requestPasswordReset(email);
    
    logUserAction(null, 'PASSWORD_RESET_REQUEST', {
      email,
      ip: req.ip
    });
    
    logApiCall('/api/v1/auth/forgot-password', 'POST', 200, Date.now() - startTime);
    
    res.json({
      success: true,
      message: 'Password reset email sent'
    });
    
  } catch (error) {
    logApiCall('/api/v1/auth/forgot-password', 'POST', error.code || 500, Date.now() - startTime);
    
    // 为了安全，即使邮箱不存在也返回成功
    res.json({
      success: true,
      message: 'Password reset email sent'
    });
  }
});

module.exports = router;
