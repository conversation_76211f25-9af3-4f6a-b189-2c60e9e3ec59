/**
 * 路由配置
 * 统一管理所有API路由
 */

const express = require('express');
const taskRoutes = require('./tasks');
const habitRoutes = require('./habits');
const authRoutes = require('./auth');
const userRoutes = require('./users');
const syncRoutes = require('./sync');
const { apiRateLimit } = require('../middleware');

const router = express.Router();

// 应用速率限制
router.use(apiRateLimit);

// API 文档路由
router.get('/', (req, res) => {
  res.json({
    message: 'TaskFlow API v1.0',
    version: '1.0.0',
    endpoints: {
      auth: '/auth',
      tasks: '/tasks',
      habits: '/habits',
      users: '/users',
      sync: '/sync',
      health: '/health'
    },
    documentation: 'https://docs.taskflow.app/api',
    support: '<EMAIL>'
  });
});

// 健康检查
router.get('/health', async (req, res) => {
  try {
    const { checkDatabaseHealth } = require('../config/database');
    const dbHealth = await checkDatabaseHealth();
    
    const health = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0',
      services: {
        database: dbHealth,
        api: {
          status: 'healthy',
          responseTime: Date.now()
        }
      }
    };
    
    const overallStatus = dbHealth.status === 'healthy' ? 200 : 503;
    res.status(overallStatus).json(health);
    
  } catch (error) {
    res.status(503).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// API 路由
router.use('/auth', authRoutes);
router.use('/tasks', taskRoutes);
router.use('/habits', habitRoutes);
router.use('/users', userRoutes);
router.use('/sync', syncRoutes);

// API 统计信息
router.get('/stats', (req, res) => {
  res.json({
    totalEndpoints: 25,
    version: '1.0.0',
    lastUpdated: '2024-01-01',
    features: [
      'User Authentication',
      'Task Management',
      'Habit Tracking',
      'Data Synchronization',
      'Push Notifications',
      'Real-time Updates'
    ]
  });
});

module.exports = router;
