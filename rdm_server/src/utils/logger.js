/**
 * 日志工具
 * 基于 Winston 的日志系统
 */

const winston = require('winston');
const path = require('path');

// 日志级别
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// 日志颜色
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'blue'
};

winston.addColors(logColors);

// 日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// 控制台格式
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, stack }) => {
    return `${timestamp} [${level}]: ${stack || message}`;
  })
);

// 创建日志目录
const logDir = process.env.LOG_FILE_PATH ? path.dirname(process.env.LOG_FILE_PATH) : './logs';

// 传输器配置
const transports = [
  // 控制台输出
  new winston.transports.Console({
    level: process.env.NODE_ENV === 'production' ? 'warn' : 'debug',
    format: consoleFormat
  })
];

// 生产环境添加文件输出
if (process.env.NODE_ENV === 'production') {
  transports.push(
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    
    // 组合日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  );
}

// 创建 logger 实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels: logLevels,
  format: logFormat,
  transports,
  exitOnError: false
});

/**
 * 请求日志中间件
 */
function requestLogger(req, res, next) {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logData = {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress
    };
    
    if (res.statusCode >= 400) {
      logger.warn('HTTP Request', logData);
    } else {
      logger.http('HTTP Request', logData);
    }
  });
  
  next();
}

/**
 * 错误日志记录
 */
function logError(error, context = {}) {
  logger.error('Application Error', {
    message: error.message,
    stack: error.stack,
    context
  });
}

/**
 * 性能监控日志
 */
function logPerformance(operation, duration, metadata = {}) {
  logger.info('Performance Metric', {
    operation,
    duration: `${duration}ms`,
    ...metadata
  });
}

/**
 * 用户操作日志
 */
function logUserAction(userId, action, details = {}) {
  logger.info('User Action', {
    userId,
    action,
    timestamp: new Date().toISOString(),
    ...details
  });
}

/**
 * 安全事件日志
 */
function logSecurityEvent(event, details = {}) {
  logger.warn('Security Event', {
    event,
    timestamp: new Date().toISOString(),
    ...details
  });
}

/**
 * 数据库操作日志
 */
function logDatabaseOperation(operation, collection, duration, metadata = {}) {
  logger.debug('Database Operation', {
    operation,
    collection,
    duration: `${duration}ms`,
    ...metadata
  });
}

/**
 * API 调用日志
 */
function logApiCall(endpoint, method, statusCode, duration, userId = null) {
  const logData = {
    endpoint,
    method,
    statusCode,
    duration: `${duration}ms`,
    timestamp: new Date().toISOString()
  };
  
  if (userId) {
    logData.userId = userId;
  }
  
  if (statusCode >= 400) {
    logger.warn('API Call Failed', logData);
  } else {
    logger.info('API Call', logData);
  }
}

/**
 * 系统健康检查日志
 */
function logHealthCheck(service, status, details = {}) {
  const logData = {
    service,
    status,
    timestamp: new Date().toISOString(),
    ...details
  };
  
  if (status === 'healthy') {
    logger.debug('Health Check', logData);
  } else {
    logger.warn('Health Check Failed', logData);
  }
}

/**
 * 清理旧日志文件
 */
function cleanupLogs() {
  logger.info('Log cleanup completed');
}

// 导出 logger 和工具函数
module.exports = {
  logger,
  requestLogger,
  logError,
  logPerformance,
  logUserAction,
  logSecurityEvent,
  logDatabaseOperation,
  logApiCall,
  logHealthCheck,
  cleanupLogs,
  
  // 直接导出常用方法
  info: logger.info.bind(logger),
  warn: logger.warn.bind(logger),
  error: logger.error.bind(logger),
  debug: logger.debug.bind(logger)
};
