/**
 * 习惯模型
 * Parse Server 数据模型定义
 */

const Parse = require('parse/node');

/**
 * 习惯频率枚举
 */
const HabitFrequency = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  CUSTOM: 'custom'
};

/**
 * 习惯类型枚举
 */
const HabitType = {
  BOOLEAN: 'boolean',      // 是/否类型
  NUMERIC: 'numeric',      // 数值类型
  DURATION: 'duration'     // 时长类型
};

/**
 * 习惯类定义
 */
class Habit extends Parse.Object {
  constructor() {
    super('Habit');
  }

  // Getter 方法
  get name() {
    return this.get('name');
  }

  get description() {
    return this.get('description');
  }

  get frequency() {
    return this.get('frequency') || HabitFrequency.DAILY;
  }

  get type() {
    return this.get('type') || HabitType.BOOLEAN;
  }

  get targetValue() {
    return this.get('targetValue') || 1;
  }

  get unit() {
    return this.get('unit');
  }

  get icon() {
    return this.get('icon');
  }

  get color() {
    return this.get('color');
  }

  get isActive() {
    return this.get('isActive') !== false;
  }

  get userId() {
    return this.get('userId');
  }

  get user() {
    return this.get('user');
  }

  get startDate() {
    return this.get('startDate');
  }

  get endDate() {
    return this.get('endDate');
  }

  get reminderTime() {
    return this.get('reminderTime');
  }

  get isReminderEnabled() {
    return this.get('isReminderEnabled') || false;
  }

  get currentStreak() {
    return this.get('currentStreak') || 0;
  }

  get bestStreak() {
    return this.get('bestStreak') || 0;
  }

  get totalCompletions() {
    return this.get('totalCompletions') || 0;
  }

  get isDeleted() {
    return this.get('isDeleted') || false;
  }

  get deletedAt() {
    return this.get('deletedAt');
  }

  // Setter 方法
  set name(value) {
    this.set('name', value);
  }

  set description(value) {
    this.set('description', value);
  }

  set frequency(value) {
    if (Object.values(HabitFrequency).includes(value)) {
      this.set('frequency', value);
    } else {
      throw new Error(`Invalid frequency: ${value}`);
    }
  }

  set type(value) {
    if (Object.values(HabitType).includes(value)) {
      this.set('type', value);
    } else {
      throw new Error(`Invalid type: ${value}`);
    }
  }

  set targetValue(value) {
    this.set('targetValue', value);
  }

  set unit(value) {
    this.set('unit', value);
  }

  set icon(value) {
    this.set('icon', value);
  }

  set color(value) {
    this.set('color', value);
  }

  set isActive(value) {
    this.set('isActive', value);
  }

  set userId(value) {
    this.set('userId', value);
  }

  set user(value) {
    this.set('user', value);
  }

  set startDate(value) {
    this.set('startDate', value);
  }

  set endDate(value) {
    this.set('endDate', value);
  }

  set reminderTime(value) {
    this.set('reminderTime', value);
  }

  set isReminderEnabled(value) {
    this.set('isReminderEnabled', value);
  }

  // 业务方法

  /**
   * 更新连续天数
   */
  updateStreak(newStreak) {
    this.set('currentStreak', newStreak);
    if (newStreak > this.bestStreak) {
      this.set('bestStreak', newStreak);
    }
  }

  /**
   * 增加完成次数
   */
  incrementCompletions() {
    this.set('totalCompletions', this.totalCompletions + 1);
  }

  /**
   * 软删除习惯
   */
  softDelete() {
    this.set('isDeleted', true);
    this.set('deletedAt', new Date());
    this.set('isActive', false);
  }

  /**
   * 恢复已删除的习惯
   */
  restore() {
    this.set('isDeleted', false);
    this.unset('deletedAt');
    this.set('isActive', true);
  }

  /**
   * 暂停习惯
   */
  pause() {
    this.set('isActive', false);
  }

  /**
   * 恢复习惯
   */
  resume() {
    this.set('isActive', true);
  }

  /**
   * 验证习惯数据
   */
  validate() {
    const errors = [];

    if (!this.name || this.name.trim().length === 0) {
      errors.push('Name is required');
    }

    if (this.name && this.name.length > 100) {
      errors.push('Name must be less than 100 characters');
    }

    if (this.description && this.description.length > 500) {
      errors.push('Description must be less than 500 characters');
    }

    if (this.frequency && !Object.values(HabitFrequency).includes(this.frequency)) {
      errors.push('Invalid frequency value');
    }

    if (this.type && !Object.values(HabitType).includes(this.type)) {
      errors.push('Invalid type value');
    }

    if (this.targetValue && (typeof this.targetValue !== 'number' || this.targetValue <= 0)) {
      errors.push('Target value must be a positive number');
    }

    if (this.startDate && this.endDate && this.startDate > this.endDate) {
      errors.push('Start date must be before end date');
    }

    return errors;
  }

  /**
   * 转换为JSON格式
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      frequency: this.frequency,
      type: this.type,
      targetValue: this.targetValue,
      unit: this.unit,
      icon: this.icon,
      color: this.color,
      isActive: this.isActive,
      userId: this.userId,
      startDate: this.startDate,
      endDate: this.endDate,
      reminderTime: this.reminderTime,
      isReminderEnabled: this.isReminderEnabled,
      currentStreak: this.currentStreak,
      bestStreak: this.bestStreak,
      totalCompletions: this.totalCompletions,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

/**
 * 习惯记录类定义
 */
class HabitRecord extends Parse.Object {
  constructor() {
    super('HabitRecord');
  }

  // Getter 方法
  get habitId() {
    return this.get('habitId');
  }

  get habit() {
    return this.get('habit');
  }

  get userId() {
    return this.get('userId');
  }

  get user() {
    return this.get('user');
  }

  get date() {
    return this.get('date');
  }

  get value() {
    return this.get('value') || 1;
  }

  get note() {
    return this.get('note');
  }

  get duration() {
    return this.get('duration');
  }

  // Setter 方法
  set habitId(value) {
    this.set('habitId', value);
  }

  set habit(value) {
    this.set('habit', value);
  }

  set userId(value) {
    this.set('userId', value);
  }

  set user(value) {
    this.set('user', value);
  }

  set date(value) {
    this.set('date', value);
  }

  set value(value) {
    this.set('value', value);
  }

  set note(value) {
    this.set('note', value);
  }

  set duration(value) {
    this.set('duration', value);
  }

  /**
   * 验证记录数据
   */
  validate() {
    const errors = [];

    if (!this.habitId) {
      errors.push('Habit ID is required');
    }

    if (!this.userId) {
      errors.push('User ID is required');
    }

    if (!this.date) {
      errors.push('Date is required');
    }

    if (this.value && (typeof this.value !== 'number' || this.value < 0)) {
      errors.push('Value must be a non-negative number');
    }

    if (this.duration && (typeof this.duration !== 'number' || this.duration < 0)) {
      errors.push('Duration must be a non-negative number');
    }

    if (this.note && this.note.length > 200) {
      errors.push('Note must be less than 200 characters');
    }

    return errors;
  }

  /**
   * 转换为JSON格式
   */
  toJSON() {
    return {
      id: this.id,
      habitId: this.habitId,
      userId: this.userId,
      date: this.date,
      value: this.value,
      note: this.note,
      duration: this.duration,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

// 注册 Parse 类
Parse.Object.registerSubclass('Habit', Habit);
Parse.Object.registerSubclass('HabitRecord', HabitRecord);

/**
 * 习惯查询辅助类
 */
class HabitQuery {
  constructor() {
    this.query = new Parse.Query(Habit);
  }

  /**
   * 按用户ID查询
   */
  byUser(userId) {
    this.query.equalTo('userId', userId);
    return this;
  }

  /**
   * 查询未删除的习惯
   */
  notDeleted() {
    this.query.notEqualTo('isDeleted', true);
    return this;
  }

  /**
   * 查询活跃的习惯
   */
  active() {
    this.query.equalTo('isActive', true);
    return this;
  }

  /**
   * 按频率查询
   */
  byFrequency(frequency) {
    this.query.equalTo('frequency', frequency);
    return this;
  }

  /**
   * 按创建时间排序
   */
  orderByCreatedAt(ascending = false) {
    if (ascending) {
      this.query.ascending('createdAt');
    } else {
      this.query.descending('createdAt');
    }
    return this;
  }

  /**
   * 分页查询
   */
  paginate(page = 1, limit = 20) {
    this.query.skip((page - 1) * limit);
    this.query.limit(limit);
    return this;
  }

  /**
   * 执行查询
   */
  async find() {
    return await this.query.find();
  }

  /**
   * 获取第一个结果
   */
  async first() {
    return await this.query.first();
  }

  /**
   * 获取查询计数
   */
  async count() {
    return await this.query.count();
  }
}

module.exports = {
  Habit,
  HabitRecord,
  HabitQuery,
  HabitFrequency,
  HabitType
};
