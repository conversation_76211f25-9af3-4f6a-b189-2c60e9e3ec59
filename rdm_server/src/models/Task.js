/**
 * 任务模型
 * Parse Server 数据模型定义
 */

const Parse = require('parse/node');

/**
 * 任务优先级枚举
 */
const TaskPriority = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
};

/**
 * 任务状态枚举
 */
const TaskStatus = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

/**
 * 任务类定义
 */
class Task extends Parse.Object {
  constructor() {
    super('Task');
  }

  // Getter 方法
  get title() {
    return this.get('title');
  }

  get description() {
    return this.get('description');
  }

  get isCompleted() {
    return this.get('isCompleted') || false;
  }

  get priority() {
    return this.get('priority') || TaskPriority.MEDIUM;
  }

  get status() {
    return this.get('status') || TaskStatus.PENDING;
  }

  get dueDate() {
    return this.get('dueDate');
  }

  get category() {
    return this.get('category');
  }

  get tags() {
    return this.get('tags') || [];
  }

  get userId() {
    return this.get('userId');
  }

  get user() {
    return this.get('user');
  }

  get completedAt() {
    return this.get('completedAt');
  }

  get estimatedDuration() {
    return this.get('estimatedDuration');
  }

  get actualDuration() {
    return this.get('actualDuration');
  }

  get isDeleted() {
    return this.get('isDeleted') || false;
  }

  get deletedAt() {
    return this.get('deletedAt');
  }

  // Setter 方法
  set title(value) {
    this.set('title', value);
  }

  set description(value) {
    this.set('description', value);
  }

  set isCompleted(value) {
    this.set('isCompleted', value);
    if (value) {
      this.set('completedAt', new Date());
      this.set('status', TaskStatus.COMPLETED);
    } else {
      this.unset('completedAt');
      this.set('status', TaskStatus.PENDING);
    }
  }

  set priority(value) {
    if (Object.values(TaskPriority).includes(value)) {
      this.set('priority', value);
    } else {
      throw new Error(`Invalid priority: ${value}`);
    }
  }

  set status(value) {
    if (Object.values(TaskStatus).includes(value)) {
      this.set('status', value);
    } else {
      throw new Error(`Invalid status: ${value}`);
    }
  }

  set dueDate(value) {
    this.set('dueDate', value);
  }

  set category(value) {
    this.set('category', value);
  }

  set tags(value) {
    this.set('tags', Array.isArray(value) ? value : []);
  }

  set userId(value) {
    this.set('userId', value);
  }

  set user(value) {
    this.set('user', value);
  }

  set estimatedDuration(value) {
    this.set('estimatedDuration', value);
  }

  set actualDuration(value) {
    this.set('actualDuration', value);
  }

  // 业务方法
  
  /**
   * 标记任务为完成
   */
  markAsCompleted() {
    this.isCompleted = true;
    this.set('completedAt', new Date());
    this.set('status', TaskStatus.COMPLETED);
  }

  /**
   * 标记任务为未完成
   */
  markAsIncomplete() {
    this.isCompleted = false;
    this.unset('completedAt');
    this.set('status', TaskStatus.PENDING);
  }

  /**
   * 软删除任务
   */
  softDelete() {
    this.set('isDeleted', true);
    this.set('deletedAt', new Date());
  }

  /**
   * 恢复已删除的任务
   */
  restore() {
    this.set('isDeleted', false);
    this.unset('deletedAt');
  }

  /**
   * 检查任务是否过期
   */
  isOverdue() {
    if (!this.dueDate || this.isCompleted) {
      return false;
    }
    return new Date() > this.dueDate;
  }

  /**
   * 获取任务的剩余时间（毫秒）
   */
  getTimeRemaining() {
    if (!this.dueDate || this.isCompleted) {
      return null;
    }
    return this.dueDate.getTime() - Date.now();
  }

  /**
   * 验证任务数据
   */
  validate() {
    const errors = [];

    if (!this.title || this.title.trim().length === 0) {
      errors.push('Title is required');
    }

    if (this.title && this.title.length > 200) {
      errors.push('Title must be less than 200 characters');
    }

    if (this.description && this.description.length > 1000) {
      errors.push('Description must be less than 1000 characters');
    }

    if (this.priority && !Object.values(TaskPriority).includes(this.priority)) {
      errors.push('Invalid priority value');
    }

    if (this.status && !Object.values(TaskStatus).includes(this.status)) {
      errors.push('Invalid status value');
    }

    if (this.dueDate && !(this.dueDate instanceof Date)) {
      errors.push('Due date must be a valid date');
    }

    if (this.estimatedDuration && (typeof this.estimatedDuration !== 'number' || this.estimatedDuration < 0)) {
      errors.push('Estimated duration must be a positive number');
    }

    if (this.actualDuration && (typeof this.actualDuration !== 'number' || this.actualDuration < 0)) {
      errors.push('Actual duration must be a positive number');
    }

    return errors;
  }

  /**
   * 转换为JSON格式
   */
  toJSON() {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      isCompleted: this.isCompleted,
      priority: this.priority,
      status: this.status,
      dueDate: this.dueDate,
      category: this.category,
      tags: this.tags,
      userId: this.userId,
      completedAt: this.completedAt,
      estimatedDuration: this.estimatedDuration,
      actualDuration: this.actualDuration,
      isOverdue: this.isOverdue(),
      timeRemaining: this.getTimeRemaining(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

// 注册 Parse 类
Parse.Object.registerSubclass('Task', Task);

/**
 * 任务查询辅助类
 */
class TaskQuery {
  constructor() {
    this.query = new Parse.Query(Task);
  }

  /**
   * 按用户ID查询
   */
  byUser(userId) {
    this.query.equalTo('userId', userId);
    return this;
  }

  /**
   * 查询未删除的任务
   */
  notDeleted() {
    this.query.notEqualTo('isDeleted', true);
    return this;
  }

  /**
   * 按完成状态查询
   */
  byCompletionStatus(isCompleted) {
    this.query.equalTo('isCompleted', isCompleted);
    return this;
  }

  /**
   * 按优先级查询
   */
  byPriority(priority) {
    this.query.equalTo('priority', priority);
    return this;
  }

  /**
   * 按截止日期范围查询
   */
  byDueDateRange(startDate, endDate) {
    if (startDate) {
      this.query.greaterThanOrEqualTo('dueDate', startDate);
    }
    if (endDate) {
      this.query.lessThanOrEqualTo('dueDate', endDate);
    }
    return this;
  }

  /**
   * 查询过期任务
   */
  overdue() {
    this.query.lessThan('dueDate', new Date());
    this.query.equalTo('isCompleted', false);
    return this;
  }

  /**
   * 按创建时间排序
   */
  orderByCreatedAt(ascending = false) {
    if (ascending) {
      this.query.ascending('createdAt');
    } else {
      this.query.descending('createdAt');
    }
    return this;
  }

  /**
   * 按截止日期排序
   */
  orderByDueDate(ascending = true) {
    if (ascending) {
      this.query.ascending('dueDate');
    } else {
      this.query.descending('dueDate');
    }
    return this;
  }

  /**
   * 分页查询
   */
  paginate(page = 1, limit = 20) {
    this.query.skip((page - 1) * limit);
    this.query.limit(limit);
    return this;
  }

  /**
   * 执行查询
   */
  async find() {
    return await this.query.find();
  }

  /**
   * 获取第一个结果
   */
  async first() {
    return await this.query.first();
  }

  /**
   * 获取查询计数
   */
  async count() {
    return await this.query.count();
  }
}

module.exports = {
  Task,
  TaskQuery,
  TaskPriority,
  TaskStatus
};
