/**
 * 习惯服务
 * 处理习惯追踪相关的业务逻辑
 */

const { Habit, HabitRecord, HabitQuery, HabitFrequency, HabitType } = require('../models/Habit');
const { logUserAction, logDatabaseOperation } = require('../utils/logger');
const Parse = require('parse/node');

class HabitService {
  /**
   * 创建新习惯
   */
  async createHabit(userId, habitData) {
    const startTime = Date.now();
    
    try {
      const {
        name,
        description,
        frequency,
        type,
        targetValue,
        unit,
        icon,
        color,
        startDate,
        endDate,
        reminderTime,
        isReminderEnabled
      } = habitData;
      
      if (!name || name.trim().length === 0) {
        throw new Parse.Error(Parse.Error.INVALID_QUERY, 'Habit name is required');
      }
      
      // 创建习惯对象
      const habit = new Habit();
      habit.name = name.trim();
      habit.description = description?.trim();
      habit.frequency = frequency || HabitFrequency.DAILY;
      habit.type = type || HabitType.BOOLEAN;
      habit.targetValue = targetValue || 1;
      habit.unit = unit?.trim();
      habit.icon = icon;
      habit.color = color;
      habit.userId = userId;
      habit.isActive = true;
      habit.currentStreak = 0;
      habit.bestStreak = 0;
      habit.totalCompletions = 0;
      
      if (startDate) {
        habit.startDate = new Date(startDate);
      }
      
      if (endDate) {
        habit.endDate = new Date(endDate);
      }
      
      if (reminderTime) {
        habit.reminderTime = reminderTime;
      }
      
      if (typeof isReminderEnabled === 'boolean') {
        habit.isReminderEnabled = isReminderEnabled;
      }
      
      // 验证习惯数据
      const validationErrors = habit.validate();
      if (validationErrors.length > 0) {
        throw new Parse.Error(Parse.Error.VALIDATION_ERROR, validationErrors.join(', '));
      }
      
      // 保存习惯
      const savedHabit = await habit.save();
      
      // 记录用户操作
      logUserAction(userId, 'CREATE_HABIT', {
        habitId: savedHabit.id,
        name: savedHabit.name
      });
      
      logDatabaseOperation('CREATE', 'Habit', Date.now() - startTime, {
        habitId: savedHabit.id
      });
      
      return savedHabit.toJSON();
      
    } catch (error) {
      logDatabaseOperation('CREATE_FAILED', 'Habit', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取用户的习惯列表
   */
  async getUserHabits(userId, options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        page = 1,
        limit = 20,
        frequency,
        isActive,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;
      
      let query = new HabitQuery()
        .byUser(userId)
        .notDeleted();
      
      // 应用筛选条件
      if (frequency) {
        query = query.byFrequency(frequency);
      }
      
      if (typeof isActive === 'boolean') {
        if (isActive) {
          query = query.active();
        } else {
          query.query.equalTo('isActive', false);
        }
      }
      
      // 应用排序
      query = query.orderByCreatedAt(sortOrder === 'asc');
      
      // 应用分页
      query = query.paginate(page, limit);
      
      // 执行查询
      const habits = await query.find();
      const total = await new HabitQuery()
        .byUser(userId)
        .notDeleted()
        .count();
      
      // 转换为JSON格式
      const habitsJson = habits.map(habit => habit.toJSON());
      
      logDatabaseOperation('QUERY', 'Habit', Date.now() - startTime, {
        userId,
        count: habits.length,
        total
      });
      
      return {
        habits: habitsJson,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
      
    } catch (error) {
      logDatabaseOperation('QUERY_FAILED', 'Habit', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取单个习惯
   */
  async getHabitById(userId, habitId) {
    const startTime = Date.now();
    
    try {
      const habit = await new HabitQuery()
        .byUser(userId)
        .notDeleted()
        .query.get(habitId);
      
      if (!habit) {
        throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, 'Habit not found');
      }
      
      logDatabaseOperation('GET', 'Habit', Date.now() - startTime, {
        habitId
      });
      
      return habit.toJSON();
      
    } catch (error) {
      logDatabaseOperation('GET_FAILED', 'Habit', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 更新习惯
   */
  async updateHabit(userId, habitId, updateData) {
    const startTime = Date.now();
    
    try {
      const habit = await new HabitQuery()
        .byUser(userId)
        .notDeleted()
        .query.get(habitId);
      
      if (!habit) {
        throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, 'Habit not found');
      }
      
      // 更新允许的字段
      const allowedFields = [
        'name', 'description', 'frequency', 'type', 'targetValue',
        'unit', 'icon', 'color', 'isActive', 'startDate', 'endDate',
        'reminderTime', 'isReminderEnabled'
      ];
      
      Object.keys(updateData).forEach(key => {
        if (allowedFields.includes(key) && updateData[key] !== undefined) {
          if (key === 'startDate' || key === 'endDate') {
            habit.set(key, new Date(updateData[key]));
          } else {
            habit.set(key, updateData[key]);
          }
        }
      });
      
      // 验证更新后的数据
      const validationErrors = habit.validate();
      if (validationErrors.length > 0) {
        throw new Parse.Error(Parse.Error.VALIDATION_ERROR, validationErrors.join(', '));
      }
      
      // 保存更新
      const updatedHabit = await habit.save();
      
      // 记录用户操作
      logUserAction(userId, 'UPDATE_HABIT', {
        habitId: updatedHabit.id,
        changes: Object.keys(updateData)
      });
      
      logDatabaseOperation('UPDATE', 'Habit', Date.now() - startTime, {
        habitId
      });
      
      return updatedHabit.toJSON();
      
    } catch (error) {
      logDatabaseOperation('UPDATE_FAILED', 'Habit', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 删除习惯（软删除）
   */
  async deleteHabit(userId, habitId) {
    const startTime = Date.now();
    
    try {
      const habit = await new HabitQuery()
        .byUser(userId)
        .notDeleted()
        .query.get(habitId);
      
      if (!habit) {
        throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, 'Habit not found');
      }
      
      // 软删除
      habit.softDelete();
      await habit.save();
      
      // 记录用户操作
      logUserAction(userId, 'DELETE_HABIT', {
        habitId: habit.id,
        name: habit.name
      });
      
      logDatabaseOperation('DELETE', 'Habit', Date.now() - startTime, {
        habitId
      });
      
      return { success: true, message: 'Habit deleted successfully' };
      
    } catch (error) {
      logDatabaseOperation('DELETE_FAILED', 'Habit', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 记录习惯完成
   */
  async recordHabitCompletion(userId, habitId, recordData) {
    const startTime = Date.now();
    
    try {
      const { date, value = 1, note, duration } = recordData;
      
      // 验证习惯存在
      const habit = await new HabitQuery()
        .byUser(userId)
        .notDeleted()
        .active()
        .query.get(habitId);
      
      if (!habit) {
        throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, 'Habit not found or inactive');
      }
      
      const recordDate = date ? new Date(date) : new Date();
      recordDate.setHours(0, 0, 0, 0); // 设置为当天开始时间
      
      // 检查是否已有当天记录
      const existingRecord = await new Parse.Query(HabitRecord)
        .equalTo('habitId', habitId)
        .equalTo('userId', userId)
        .equalTo('date', recordDate)
        .first();
      
      let record;
      if (existingRecord) {
        // 更新现有记录
        record = existingRecord;
        record.value = value;
        if (note) record.note = note;
        if (duration) record.duration = duration;
      } else {
        // 创建新记录
        record = new HabitRecord();
        record.habitId = habitId;
        record.userId = userId;
        record.date = recordDate;
        record.value = value;
        if (note) record.note = note;
        if (duration) record.duration = duration;
      }
      
      // 验证记录数据
      const validationErrors = record.validate();
      if (validationErrors.length > 0) {
        throw new Parse.Error(Parse.Error.VALIDATION_ERROR, validationErrors.join(', '));
      }
      
      // 保存记录
      const savedRecord = await record.save();
      
      // 更新习惯统计
      await this.updateHabitStatistics(habit);
      
      // 记录用户操作
      logUserAction(userId, 'RECORD_HABIT', {
        habitId,
        recordId: savedRecord.id,
        value
      });
      
      logDatabaseOperation('CREATE', 'HabitRecord', Date.now() - startTime, {
        habitId,
        recordId: savedRecord.id
      });
      
      return savedRecord.toJSON();
      
    } catch (error) {
      logDatabaseOperation('CREATE_FAILED', 'HabitRecord', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 更新习惯统计信息
   */
  async updateHabitStatistics(habit) {
    try {
      // 获取所有记录
      const records = await new Parse.Query(HabitRecord)
        .equalTo('habitId', habit.id)
        .ascending('date')
        .find();
      
      // 计算总完成次数
      const totalCompletions = records.length;
      
      // 计算连续天数
      const { currentStreak, bestStreak } = this.calculateStreaks(records);
      
      // 更新习惯统计
      habit.set('totalCompletions', totalCompletions);
      habit.updateStreak(currentStreak);
      if (bestStreak > habit.bestStreak) {
        habit.set('bestStreak', bestStreak);
      }
      
      await habit.save();
      
    } catch (error) {
      console.error('Failed to update habit statistics:', error);
    }
  }

  /**
   * 计算连续天数
   */
  calculateStreaks(records) {
    if (records.length === 0) {
      return { currentStreak: 0, bestStreak: 0 };
    }
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    let currentStreak = 0;
    let bestStreak = 0;
    let tempStreak = 0;
    
    // 从最新记录开始计算当前连续天数
    for (let i = records.length - 1; i >= 0; i--) {
      const recordDate = new Date(records[i].get('date'));
      const expectedDate = new Date(today.getTime() - currentStreak * 24 * 60 * 60 * 1000);
      
      if (recordDate.getTime() === expectedDate.getTime()) {
        currentStreak++;
      } else {
        break;
      }
    }
    
    // 计算历史最佳连续天数
    let lastDate = null;
    for (const record of records) {
      const recordDate = new Date(record.get('date'));
      
      if (lastDate && recordDate.getTime() === lastDate.getTime() + 24 * 60 * 60 * 1000) {
        tempStreak++;
      } else {
        tempStreak = 1;
      }
      
      bestStreak = Math.max(bestStreak, tempStreak);
      lastDate = recordDate;
    }
    
    return { currentStreak, bestStreak };
  }
}

module.exports = new HabitService();
