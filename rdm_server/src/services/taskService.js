/**
 * 任务服务
 * 处理任务相关的业务逻辑
 */

const { Task, TaskQuery, TaskPriority, TaskStatus } = require('../models/Task');
const { logUserAction, logDatabaseOperation } = require('../utils/logger');
const Parse = require('parse/node');

class TaskService {
  /**
   * 创建新任务
   */
  async createTask(userId, taskData) {
    const startTime = Date.now();
    
    try {
      // 验证输入数据
      const { title, description, priority, dueDate, category, tags, estimatedDuration } = taskData;
      
      if (!title || title.trim().length === 0) {
        throw new Parse.Error(Parse.Error.INVALID_QUERY, 'Task title is required');
      }
      
      // 创建任务对象
      const task = new Task();
      task.title = title.trim();
      task.description = description?.trim();
      task.priority = priority || TaskPriority.MEDIUM;
      task.status = TaskStatus.PENDING;
      task.userId = userId;
      task.isCompleted = false;
      
      if (dueDate) {
        task.dueDate = new Date(dueDate);
      }
      
      if (category) {
        task.category = category.trim();
      }
      
      if (tags && Array.isArray(tags)) {
        task.tags = tags.filter(tag => tag && tag.trim().length > 0);
      }
      
      if (estimatedDuration && typeof estimatedDuration === 'number') {
        task.estimatedDuration = estimatedDuration;
      }
      
      // 验证任务数据
      const validationErrors = task.validate();
      if (validationErrors.length > 0) {
        throw new Parse.Error(Parse.Error.VALIDATION_ERROR, validationErrors.join(', '));
      }
      
      // 保存任务
      const savedTask = await task.save();
      
      // 记录用户操作
      logUserAction(userId, 'CREATE_TASK', {
        taskId: savedTask.id,
        title: savedTask.title
      });
      
      // 记录数据库操作
      logDatabaseOperation('CREATE', 'Task', Date.now() - startTime, {
        taskId: savedTask.id
      });
      
      return savedTask.toJSON();
      
    } catch (error) {
      logDatabaseOperation('CREATE_FAILED', 'Task', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取用户的任务列表
   */
  async getUserTasks(userId, options = {}) {
    const startTime = Date.now();
    
    try {
      const {
        page = 1,
        limit = 20,
        status,
        priority,
        isCompleted,
        category,
        dueDateStart,
        dueDateEnd,
        search,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;
      
      let query = new TaskQuery()
        .byUser(userId)
        .notDeleted();
      
      // 应用筛选条件
      if (status) {
        query.query.equalTo('status', status);
      }
      
      if (priority) {
        query.query.equalTo('priority', priority);
      }
      
      if (typeof isCompleted === 'boolean') {
        query = query.byCompletionStatus(isCompleted);
      }
      
      if (category) {
        query.query.equalTo('category', category);
      }
      
      if (dueDateStart || dueDateEnd) {
        query = query.byDueDateRange(
          dueDateStart ? new Date(dueDateStart) : null,
          dueDateEnd ? new Date(dueDateEnd) : null
        );
      }
      
      if (search) {
        query.query.matches('title', search, 'i');
      }
      
      // 应用排序
      if (sortBy === 'dueDate') {
        query = query.orderByDueDate(sortOrder === 'asc');
      } else {
        query = query.orderByCreatedAt(sortOrder === 'asc');
      }
      
      // 应用分页
      query = query.paginate(page, limit);
      
      // 执行查询
      const tasks = await query.find();
      const total = await new TaskQuery()
        .byUser(userId)
        .notDeleted()
        .count();
      
      // 转换为JSON格式
      const tasksJson = tasks.map(task => task.toJSON());
      
      // 记录数据库操作
      logDatabaseOperation('QUERY', 'Task', Date.now() - startTime, {
        userId,
        count: tasks.length,
        total
      });
      
      return {
        tasks: tasksJson,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
      
    } catch (error) {
      logDatabaseOperation('QUERY_FAILED', 'Task', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取单个任务
   */
  async getTaskById(userId, taskId) {
    const startTime = Date.now();
    
    try {
      const task = await new TaskQuery()
        .byUser(userId)
        .notDeleted()
        .query.get(taskId);
      
      if (!task) {
        throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, 'Task not found');
      }
      
      logDatabaseOperation('GET', 'Task', Date.now() - startTime, {
        taskId
      });
      
      return task.toJSON();
      
    } catch (error) {
      logDatabaseOperation('GET_FAILED', 'Task', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 更新任务
   */
  async updateTask(userId, taskId, updateData) {
    const startTime = Date.now();
    
    try {
      const task = await new TaskQuery()
        .byUser(userId)
        .notDeleted()
        .query.get(taskId);
      
      if (!task) {
        throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, 'Task not found');
      }
      
      // 更新允许的字段
      const allowedFields = [
        'title', 'description', 'priority', 'status', 'dueDate',
        'category', 'tags', 'isCompleted', 'estimatedDuration', 'actualDuration'
      ];
      
      Object.keys(updateData).forEach(key => {
        if (allowedFields.includes(key) && updateData[key] !== undefined) {
          if (key === 'isCompleted') {
            task.isCompleted = updateData[key];
          } else if (key === 'dueDate' && updateData[key]) {
            task.dueDate = new Date(updateData[key]);
          } else if (key === 'tags' && Array.isArray(updateData[key])) {
            task.tags = updateData[key].filter(tag => tag && tag.trim().length > 0);
          } else {
            task.set(key, updateData[key]);
          }
        }
      });
      
      // 验证更新后的数据
      const validationErrors = task.validate();
      if (validationErrors.length > 0) {
        throw new Parse.Error(Parse.Error.VALIDATION_ERROR, validationErrors.join(', '));
      }
      
      // 保存更新
      const updatedTask = await task.save();
      
      // 记录用户操作
      logUserAction(userId, 'UPDATE_TASK', {
        taskId: updatedTask.id,
        changes: Object.keys(updateData)
      });
      
      logDatabaseOperation('UPDATE', 'Task', Date.now() - startTime, {
        taskId
      });
      
      return updatedTask.toJSON();
      
    } catch (error) {
      logDatabaseOperation('UPDATE_FAILED', 'Task', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 删除任务（软删除）
   */
  async deleteTask(userId, taskId) {
    const startTime = Date.now();
    
    try {
      const task = await new TaskQuery()
        .byUser(userId)
        .notDeleted()
        .query.get(taskId);
      
      if (!task) {
        throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, 'Task not found');
      }
      
      // 软删除
      task.softDelete();
      await task.save();
      
      // 记录用户操作
      logUserAction(userId, 'DELETE_TASK', {
        taskId: task.id,
        title: task.title
      });
      
      logDatabaseOperation('DELETE', 'Task', Date.now() - startTime, {
        taskId
      });
      
      return { success: true, message: 'Task deleted successfully' };
      
    } catch (error) {
      logDatabaseOperation('DELETE_FAILED', 'Task', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 批量操作任务
   */
  async batchUpdateTasks(userId, taskIds, operation, data = {}) {
    const startTime = Date.now();
    
    try {
      if (!Array.isArray(taskIds) || taskIds.length === 0) {
        throw new Parse.Error(Parse.Error.INVALID_QUERY, 'Task IDs array is required');
      }
      
      const tasks = await new TaskQuery()
        .byUser(userId)
        .notDeleted()
        .query.containedIn('objectId', taskIds)
        .find();
      
      if (tasks.length === 0) {
        throw new Parse.Error(Parse.Error.OBJECT_NOT_FOUND, 'No tasks found');
      }
      
      const results = [];
      
      for (const task of tasks) {
        try {
          switch (operation) {
            case 'complete':
              task.markAsCompleted();
              break;
            case 'incomplete':
              task.markAsIncomplete();
              break;
            case 'delete':
              task.softDelete();
              break;
            case 'update':
              Object.keys(data).forEach(key => {
                if (data[key] !== undefined) {
                  task.set(key, data[key]);
                }
              });
              break;
            default:
              throw new Parse.Error(Parse.Error.INVALID_QUERY, `Invalid operation: ${operation}`);
          }
          
          await task.save();
          results.push({ id: task.id, success: true });
          
        } catch (error) {
          results.push({ id: task.id, success: false, error: error.message });
        }
      }
      
      // 记录用户操作
      logUserAction(userId, 'BATCH_UPDATE_TASKS', {
        operation,
        taskCount: taskIds.length,
        successCount: results.filter(r => r.success).length
      });
      
      logDatabaseOperation('BATCH_UPDATE', 'Task', Date.now() - startTime, {
        operation,
        taskCount: taskIds.length
      });
      
      return {
        success: true,
        results,
        summary: {
          total: taskIds.length,
          successful: results.filter(r => r.success).length,
          failed: results.filter(r => !r.success).length
        }
      };
      
    } catch (error) {
      logDatabaseOperation('BATCH_UPDATE_FAILED', 'Task', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 获取任务统计信息
   */
  async getTaskStatistics(userId, period = 'week') {
    const startTime = Date.now();
    
    try {
      const now = new Date();
      let startDate;
      
      switch (period) {
        case 'day':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        default:
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      }
      
      const baseQuery = new TaskQuery().byUser(userId).notDeleted();
      
      // 总任务数
      const totalTasks = await baseQuery.count();
      
      // 已完成任务数
      const completedTasks = await new TaskQuery()
        .byUser(userId)
        .notDeleted()
        .byCompletionStatus(true)
        .count();
      
      // 过期任务数
      const overdueTasks = await new TaskQuery()
        .byUser(userId)
        .notDeleted()
        .overdue()
        .count();
      
      // 本期完成的任务数
      const periodQuery = new TaskQuery()
        .byUser(userId)
        .notDeleted()
        .byCompletionStatus(true);
      periodQuery.query.greaterThanOrEqualTo('completedAt', startDate);
      const periodCompletedTasks = await periodQuery.count();
      
      // 按优先级统计
      const highPriorityTasks = await new TaskQuery()
        .byUser(userId)
        .notDeleted()
        .byPriority(TaskPriority.HIGH)
        .count();
      
      const mediumPriorityTasks = await new TaskQuery()
        .byUser(userId)
        .notDeleted()
        .byPriority(TaskPriority.MEDIUM)
        .count();
      
      const lowPriorityTasks = await new TaskQuery()
        .byUser(userId)
        .notDeleted()
        .byPriority(TaskPriority.LOW)
        .count();
      
      logDatabaseOperation('STATISTICS', 'Task', Date.now() - startTime, {
        userId,
        period
      });
      
      return {
        total: totalTasks,
        completed: completedTasks,
        pending: totalTasks - completedTasks,
        overdue: overdueTasks,
        completionRate: totalTasks > 0 ? (completedTasks / totalTasks * 100).toFixed(1) : 0,
        periodCompleted: periodCompletedTasks,
        byPriority: {
          high: highPriorityTasks,
          medium: mediumPriorityTasks,
          low: lowPriorityTasks
        }
      };
      
    } catch (error) {
      logDatabaseOperation('STATISTICS_FAILED', 'Task', Date.now() - startTime, {
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = new TaskService();
