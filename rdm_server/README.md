# TaskFlow Server

TaskFlow 后端服务器，基于 Express.js 和 Parse Server 构建的任务管理和习惯追踪应用后端。

## 技术栈

- **Node.js** - JavaScript 运行时
- **Express.js** - Web 应用框架
- **Parse Server** - 开源后端即服务 (BaaS)
- **MongoDB** - NoSQL 数据库
- **Redis** - 缓存和会话存储
- **Winston** - 日志管理

## 功能特性

- 🔐 用户认证和授权（邮箱/密码、Apple ID）
- 📝 任务管理（CRUD、批量操作、统计）
- 🎯 习惯追踪（创建、记录、统计分析）
- 🔄 数据同步（增量同步、冲突解决）
- 📱 推送通知支持
- 🛡️ 安全防护（速率限制、输入验证）
- 📊 详细日志和监控
- 🚀 高性能和可扩展性

## 快速开始

### 环境要求

- Node.js >= 18.0.0
- MongoDB >= 5.0
- Redis >= 6.0 (可选，用于缓存)

### 安装依赖

```bash
npm install
```

### 环境配置

1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置必要的环境变量：
```bash
# 数据库配置
MONGODB_URI=mongodb://localhost:27017/taskflow

# Parse Server 配置
PARSE_APP_ID=your-app-id
PARSE_MASTER_KEY=your-master-key

# JWT 配置
JWT_SECRET=your-jwt-secret

# 其他配置...
```

### 启动服务

#### 开发模式
```bash
npm run dev
```

#### 生产模式
```bash
npm start
```

服务器将在 `http://localhost:3000` 启动。

### 访问服务

- **API 根路径**: `http://localhost:3000/api/v1`
- **Parse Dashboard**: `http://localhost:3000/dashboard` (开发模式)
- **健康检查**: `http://localhost:3000/api/v1/health`

## API 文档

### 认证端点

| 方法 | 端点 | 描述 |
|------|------|------|
| POST | `/auth/register` | 用户注册 |
| POST | `/auth/login` | 用户登录 |
| POST | `/auth/apple` | Apple ID 登录 |
| POST | `/auth/logout` | 用户登出 |
| POST | `/auth/forgot-password` | 忘记密码 |

### 任务管理

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/tasks` | 获取任务列表 |
| POST | `/tasks` | 创建新任务 |
| GET | `/tasks/:id` | 获取单个任务 |
| PUT | `/tasks/:id` | 更新任务 |
| DELETE | `/tasks/:id` | 删除任务 |
| POST | `/tasks/batch` | 批量操作任务 |
| GET | `/tasks/statistics` | 获取任务统计 |

### 习惯追踪

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/habits` | 获取习惯列表 |
| POST | `/habits` | 创建新习惯 |
| GET | `/habits/:id` | 获取单个习惯 |
| PUT | `/habits/:id` | 更新习惯 |
| DELETE | `/habits/:id` | 删除习惯 |
| POST | `/habits/:id/records` | 记录习惯完成 |

### 用户管理

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/users/me` | 获取用户信息 |
| PUT | `/users/me` | 更新用户信息 |
| POST | `/users/change-password` | 修改密码 |
| PUT | `/users/me/notifications` | 更新通知设置 |
| DELETE | `/users/me` | 删除账户 |
| GET | `/users/me/stats` | 获取用户统计 |

### 数据同步

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/sync` | 获取增量同步数据 |
| POST | `/sync` | 上传本地变更 |

## 数据模型

### 用户 (User)
```javascript
{
  username: String,
  email: String,
  password: String,
  fullName: String,
  isEmailVerified: Boolean,
  isActive: Boolean,
  authProvider: String,
  timezone: String,
  preferences: Object,
  notificationSettings: Object,
  lastLoginAt: Date,
  lastLoginIP: String
}
```

### 任务 (Task)
```javascript
{
  title: String,
  description: String,
  isCompleted: Boolean,
  priority: String, // 'high', 'medium', 'low'
  status: String, // 'pending', 'in_progress', 'completed', 'cancelled'
  dueDate: Date,
  category: String,
  tags: Array,
  userId: String,
  estimatedDuration: Number,
  actualDuration: Number,
  completedAt: Date,
  isDeleted: Boolean,
  deletedAt: Date
}
```

### 习惯 (Habit)
```javascript
{
  name: String,
  description: String,
  frequency: String, // 'daily', 'weekly', 'monthly', 'custom'
  type: String, // 'boolean', 'numeric', 'duration'
  targetValue: Number,
  unit: String,
  icon: String,
  color: String,
  isActive: Boolean,
  userId: String,
  startDate: Date,
  endDate: Date,
  reminderTime: String,
  isReminderEnabled: Boolean,
  currentStreak: Number,
  bestStreak: Number,
  totalCompletions: Number,
  isDeleted: Boolean,
  deletedAt: Date
}
```

### 习惯记录 (HabitRecord)
```javascript
{
  habitId: String,
  userId: String,
  date: Date,
  value: Number,
  note: String,
  duration: Number
}
```

## 开发指南

### 项目结构
```
src/
├── cloud/          # Parse Server Cloud Code
├── config/         # 配置文件
├── middleware/     # 中间件
├── models/         # 数据模型
├── routes/         # API 路由
├── services/       # 业务逻辑服务
└── utils/          # 工具函数
```

### 代码规范

- 使用 ESLint 进行代码检查
- 遵循 Airbnb JavaScript 风格指南
- 所有 API 端点必须有输入验证
- 所有数据库操作必须有错误处理
- 重要操作必须记录日志

### 测试

```bash
# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

### 代码检查

```bash
# 运行 ESLint
npm run lint

# 自动修复 ESLint 错误
npm run lint:fix
```

## 部署

### Docker 部署

1. 构建镜像：
```bash
docker build -t taskflow-server .
```

2. 运行容器：
```bash
docker run -p 3000:3000 --env-file .env taskflow-server
```

### 生产环境配置

1. 设置环境变量：
```bash
NODE_ENV=production
PARSE_DASHBOARD_ALLOW_INSECURE_HTTP=false
LOG_LEVEL=warn
```

2. 配置反向代理（Nginx）
3. 设置 SSL 证书
4. 配置数据库备份
5. 设置监控和日志收集

## 监控和日志

### 健康检查

访问 `/api/v1/health` 获取服务状态：

```json
{
  "status": "OK",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600,
  "environment": "production",
  "services": {
    "database": {
      "status": "healthy"
    },
    "api": {
      "status": "healthy"
    }
  }
}
```

### 日志级别

- `error`: 错误信息
- `warn`: 警告信息
- `info`: 一般信息
- `debug`: 调试信息

### 性能监控

- API 响应时间
- 数据库查询性能
- 内存使用情况
- 错误率统计

## 安全

### 认证和授权

- JWT Token 认证
- 会话管理
- 权限验证
- Apple ID 集成

### 安全措施

- 速率限制
- 输入验证和清理
- SQL 注入防护
- XSS 防护
- CSRF 防护
- 安全头设置

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 MongoDB 服务是否运行
   - 验证连接字符串是否正确

2. **Parse Server 启动失败**
   - 检查 App ID 和 Master Key 配置
   - 验证数据库权限

3. **API 请求失败**
   - 检查认证 Token 是否有效
   - 验证请求格式是否正确

### 日志查看

```bash
# 查看实时日志
tail -f logs/combined.log

# 查看错误日志
tail -f logs/error.log
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

- 项目主页: https://github.com/taskflow/taskflow-server
- 问题反馈: https://github.com/taskflow/taskflow-server/issues
- 邮箱: <EMAIL>
