//
//  TaskFilterView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct TaskFilterView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var taskService = TaskService.shared
    
    @Binding var selectedFilter: TaskFilter
    
    @State private var selectedPriority: Task.Priority?
    @State private var selectedStatus: Task.Status?
    @State private var selectedCategory: String?
    @State private var isCompletedFilter: Bool?
    @State private var dueDateFilter: DueDateFilter = .any
    @State private var sortBy: SortOption = .createdAt
    @State private var sortOrder: SortOrder = .descending
    
    var body: some View {
        NavigationView {
            Form {
                // Quick Filters
                quickFiltersSection
                
                // Priority Filter
                priorityFilterSection
                
                // Status Filter
                statusFilterSection
                
                // Category Filter
                categoryFilterSection
                
                // Due Date Filter
                dueDateFilterSection
                
                // Completion Filter
                completionFilterSection
                
                // Sort Options
                sortOptionsSection
            }
            .navigationTitle("筛选和排序")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("重置") {
                        resetFilters()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        applyFilters()
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - Quick Filters Section
    
    private var quickFiltersSection: some View {
        Section("快速筛选") {
            ForEach(TaskFilter.allCases, id: \.self) { filter in
                HStack {
                    Button(action: {
                        selectedFilter = filter
                        setQuickFilter(filter)
                    }) {
                        HStack {
                            Text(filter.displayName)
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            Text("\(getFilterCount(filter))")
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(Color.accentColor.opacity(0.2))
                                .foregroundColor(.accentColor)
                                .cornerRadius(8)
                        }
                    }
                    
                    if selectedFilter == filter {
                        Image(systemName: "checkmark")
                            .foregroundColor(.accentColor)
                    }
                }
            }
        }
    }
    
    // MARK: - Priority Filter Section
    
    private var priorityFilterSection: some View {
        Section("优先级") {
            Picker("优先级", selection: $selectedPriority) {
                Text("全部").tag(nil as Task.Priority?)
                
                ForEach(Task.Priority.allCases, id: \.self) { priority in
                    HStack {
                        Image(systemName: priority.iconName)
                            .foregroundColor(Color.fromColorName(priority.color))
                        Text(priority.displayName)
                    }
                    .tag(priority as Task.Priority?)
                }
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
    
    // MARK: - Status Filter Section
    
    private var statusFilterSection: some View {
        Section("状态") {
            Picker("状态", selection: $selectedStatus) {
                Text("全部").tag(nil as Task.Status?)
                
                ForEach(Task.Status.allCases, id: \.self) { status in
                    HStack {
                        Image(systemName: status.iconName)
                            .foregroundColor(Color.fromColorName(status.color))
                        Text(status.displayName)
                    }
                    .tag(status as Task.Status?)
                }
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
    
    // MARK: - Category Filter Section
    
    private var categoryFilterSection: some View {
        Section("分类") {
            Picker("分类", selection: $selectedCategory) {
                Text("全部").tag(nil as String?)
                
                ForEach(taskService.categories, id: \.self) { category in
                    Text(category).tag(category as String?)
                }
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
    
    // MARK: - Due Date Filter Section
    
    private var dueDateFilterSection: some View {
        Section("截止时间") {
            Picker("截止时间", selection: $dueDateFilter) {
                ForEach(DueDateFilter.allCases, id: \.self) { filter in
                    Text(filter.displayName).tag(filter)
                }
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
    
    // MARK: - Completion Filter Section
    
    private var completionFilterSection: some View {
        Section("完成状态") {
            Picker("完成状态", selection: $isCompletedFilter) {
                Text("全部").tag(nil as Bool?)
                Text("已完成").tag(true as Bool?)
                Text("未完成").tag(false as Bool?)
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    
    // MARK: - Sort Options Section
    
    private var sortOptionsSection: some View {
        Section("排序") {
            Picker("排序方式", selection: $sortBy) {
                ForEach(SortOption.allCases, id: \.self) { option in
                    Text(option.displayName).tag(option)
                }
            }
            .pickerStyle(MenuPickerStyle())
            
            Picker("排序顺序", selection: $sortOrder) {
                ForEach(SortOrder.allCases, id: \.self) { order in
                    Text(order.displayName).tag(order)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    
    // MARK: - Helper Methods
    
    private func getFilterCount(_ filter: TaskFilter) -> Int {
        switch filter {
        case .all:
            return taskService.tasks.count
        case .pending:
            return taskService.pendingTasks.count
        case .completed:
            return taskService.completedTasks.count
        case .overdue:
            return taskService.overdueTasks.count
        case .today:
            return taskService.todayTasks.count
        case .highPriority:
            return taskService.highPriorityTasks.count
        }
    }
    
    private func setQuickFilter(_ filter: TaskFilter) {
        resetFilters()
        
        switch filter {
        case .all:
            break
        case .pending:
            isCompletedFilter = false
        case .completed:
            isCompletedFilter = true
        case .overdue:
            dueDateFilter = .overdue
        case .today:
            dueDateFilter = .today
        case .highPriority:
            selectedPriority = .high
        }
    }
    
    private func resetFilters() {
        selectedPriority = nil
        selectedStatus = nil
        selectedCategory = nil
        isCompletedFilter = nil
        dueDateFilter = .any
        sortBy = .createdAt
        sortOrder = .descending
    }
    
    private func applyFilters() {
        Task {
            await taskService.fetchTasks(
                priority: selectedPriority,
                isCompleted: isCompletedFilter,
                category: selectedCategory,
                sortBy: sortBy.rawValue,
                sortOrder: sortOrder.rawValue
            )
        }
    }
}

// MARK: - Supporting Types

enum DueDateFilter: CaseIterable {
    case any
    case today
    case tomorrow
    case thisWeek
    case overdue
    case noDueDate
    
    var displayName: String {
        switch self {
        case .any:
            return "全部"
        case .today:
            return "今天"
        case .tomorrow:
            return "明天"
        case .thisWeek:
            return "本周"
        case .overdue:
            return "已过期"
        case .noDueDate:
            return "无截止时间"
        }
    }
}

enum SortOption: String, CaseIterable {
    case createdAt = "createdAt"
    case updatedAt = "updatedAt"
    case dueDate = "dueDate"
    case priority = "priority"
    case title = "title"
    
    var displayName: String {
        switch self {
        case .createdAt:
            return "创建时间"
        case .updatedAt:
            return "更新时间"
        case .dueDate:
            return "截止时间"
        case .priority:
            return "优先级"
        case .title:
            return "标题"
        }
    }
}

enum SortOrder: String, CaseIterable {
    case ascending = "asc"
    case descending = "desc"
    
    var displayName: String {
        switch self {
        case .ascending:
            return "升序"
        case .descending:
            return "降序"
        }
    }
}

#Preview {
    TaskFilterView(selectedFilter: .constant(.all))
}
