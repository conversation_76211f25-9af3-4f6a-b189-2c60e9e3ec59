//
//  EditTaskView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct EditTaskView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var taskService = TaskService.shared
    
    let originalTask: Task
    let onUpdate: (Task) -> Void
    
    @State private var title: String
    @State private var description: String
    @State private var priority: Task.Priority
    @State private var status: Task.Status
    @State private var dueDate: Date
    @State private var hasDueDate: Bool
    @State private var category: String
    @State private var tags: [String]
    @State private var newTag = ""
    @State private var estimatedHours: Int
    @State private var estimatedMinutes: Int
    @State private var hasEstimatedDuration: Bool
    
    @State private var isLoading = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    init(task: Task, onUpdate: @escaping (Task) -> Void) {
        self.originalTask = task
        self.onUpdate = onUpdate
        
        // Initialize state
        self._title = State(initialValue: task.title)
        self._description = State(initialValue: task.description ?? "")
        self._priority = State(initialValue: task.priority)
        self._status = State(initialValue: task.status)
        self._dueDate = State(initialValue: task.dueDate ?? Date())
        self._hasDueDate = State(initialValue: task.dueDate != nil)
        self._category = State(initialValue: task.category ?? "")
        self._tags = State(initialValue: task.tags)
        
        // Initialize duration
        let totalMinutes = Int((task.estimatedDuration ?? 0) / 60)
        self._estimatedHours = State(initialValue: totalMinutes / 60)
        self._estimatedMinutes = State(initialValue: totalMinutes % 60)
        self._hasEstimatedDuration = State(initialValue: task.estimatedDuration != nil)
    }
    
    var body: some View {
        NavigationView {
            Form {
                // Basic Information
                basicInfoSection
                
                // Status and Priority
                statusPrioritySection
                
                // Due Date
                dueDateSection
                
                // Category and Tags
                categoryTagsSection
                
                // Estimated Duration
                estimatedDurationSection
            }
            .navigationTitle("编辑任务")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveTask()
                    }
                    .disabled(!isFormValid || isLoading)
                }
            }
        }
        .alert("错误", isPresented: $showingError) {
            Button("确定") {}
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - Basic Information Section
    
    private var basicInfoSection: some View {
        Section("基本信息") {
            TextField("任务标题", text: $title)
                .textInputAutocapitalization(.sentences)
            
            TextField("任务描述（可选）", text: $description, axis: .vertical)
                .lineLimit(3...6)
                .textInputAutocapitalization(.sentences)
        }
    }
    
    // MARK: - Status and Priority Section
    
    private var statusPrioritySection: some View {
        Section("状态和优先级") {
            // Status Picker
            Picker("状态", selection: $status) {
                ForEach(Task.Status.allCases, id: \.self) { status in
                    HStack {
                        Image(systemName: status.iconName)
                            .foregroundColor(Color.fromColorName(status.color))
                        Text(status.displayName)
                    }
                    .tag(status)
                }
            }
            
            // Priority Picker
            Picker("优先级", selection: $priority) {
                ForEach(Task.Priority.allCases, id: \.self) { priority in
                    HStack {
                        Image(systemName: priority.iconName)
                            .foregroundColor(Color.fromColorName(priority.color))
                        Text(priority.displayName)
                    }
                    .tag(priority)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    
    // MARK: - Due Date Section
    
    private var dueDateSection: some View {
        Section("截止时间") {
            Toggle("设置截止时间", isOn: $hasDueDate)
            
            if hasDueDate {
                DatePicker(
                    "截止时间",
                    selection: $dueDate,
                    in: Date()...,
                    displayedComponents: [.date, .hourAndMinute]
                )
                .datePickerStyle(CompactDatePickerStyle())
            }
        }
    }
    
    // MARK: - Category and Tags Section
    
    private var categoryTagsSection: some View {
        Section("分类和标签") {
            TextField("分类（可选）", text: $category)
                .textInputAutocapitalization(.words)
            
            // Tags Input
            HStack {
                TextField("添加标签", text: $newTag)
                    .textInputAutocapitalization(.never)
                    .onSubmit {
                        addTag()
                    }
                
                Button("添加", action: addTag)
                    .disabled(newTag.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }
            
            // Tags Display
            if !tags.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack {
                        ForEach(tags, id: \.self) { tag in
                            TagView(tag: tag) {
                                removeTag(tag)
                            }
                        }
                    }
                    .padding(.horizontal, 4)
                }
            }
        }
    }
    
    // MARK: - Estimated Duration Section
    
    private var estimatedDurationSection: some View {
        Section("预估时长") {
            Toggle("设置预估时长", isOn: $hasEstimatedDuration)
            
            if hasEstimatedDuration {
                HStack {
                    Picker("小时", selection: $estimatedHours) {
                        ForEach(0...23, id: \.self) { hour in
                            Text("\(hour) 小时").tag(hour)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(maxWidth: .infinity)
                    
                    Picker("分钟", selection: $estimatedMinutes) {
                        ForEach([0, 15, 30, 45], id: \.self) { minute in
                            Text("\(minute) 分钟").tag(minute)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(maxWidth: .infinity)
                }
                .frame(height: 120)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    private var estimatedDuration: TimeInterval? {
        guard hasEstimatedDuration else { return nil }
        let totalMinutes = estimatedHours * 60 + estimatedMinutes
        return totalMinutes > 0 ? TimeInterval(totalMinutes * 60) : nil
    }
    
    private var hasChanges: Bool {
        title != originalTask.title ||
        description != (originalTask.description ?? "") ||
        priority != originalTask.priority ||
        status != originalTask.status ||
        (hasDueDate ? dueDate : nil) != originalTask.dueDate ||
        category != (originalTask.category ?? "") ||
        tags != originalTask.tags ||
        estimatedDuration != originalTask.estimatedDuration
    }
    
    // MARK: - Actions
    
    private func addTag() {
        let trimmedTag = newTag.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedTag.isEmpty && !tags.contains(trimmedTag) else { return }
        
        tags.append(trimmedTag)
        newTag = ""
    }
    
    private func removeTag(_ tag: String) {
        tags.removeAll { $0 == tag }
    }
    
    private func saveTask() {
        guard hasChanges else {
            dismiss()
            return
        }
        
        isLoading = true
        
        let updateRequest = TaskUpdateRequest(
            title: title.trimmingCharacters(in: .whitespacesAndNewlines),
            description: description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : description.trimmingCharacters(in: .whitespacesAndNewlines),
            priority: priority,
            status: status,
            dueDate: hasDueDate ? dueDate : nil,
            category: category.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : category.trimmingCharacters(in: .whitespacesAndNewlines),
            tags: tags,
            estimatedDuration: estimatedDuration
        )
        
        Task {
            do {
                let updatedTask = try await taskService.updateTask(originalTask, with: updateRequest)
                
                await MainActor.run {
                    isLoading = false
                    onUpdate(updatedTask)
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }
    }
}

#Preview {
    EditTaskView(task: Task.preview()) { _ in }
}
