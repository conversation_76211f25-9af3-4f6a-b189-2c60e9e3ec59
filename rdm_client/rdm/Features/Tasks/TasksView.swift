//
//  TasksView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct TasksView: View {
    @StateObject private var taskService = TaskService.shared
    @State private var showingAddTask = false
    @State private var showingFilterSheet = false
    @State private var searchText = ""
    @State private var selectedFilter: TaskFilter = .all
    @State private var selectedTask: Task?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search Bar
                searchBar
                
                // Filter Chips
                filterChips
                
                // Task List
                taskList
            }
            .navigationTitle("任务")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>(action: { showingFilterSheet = true }) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingAddTask = true }) {
                        Image(systemName: "plus")
                    }
                }
            }
            .refreshable {
                await taskService.refreshTasks()
            }
        }
        .sheet(isPresented: $showingAddTask) {
            AddTaskView()
        }
        .sheet(isPresented: $showingFilterSheet) {
            TaskFilterView(selectedFilter: $selectedFilter)
        }
        .sheet(item: $selectedTask) { task in
            TaskDetailView(task: task)
        }
        .onAppear {
            Task {
                await taskService.fetchTasks()
            }
        }
    }
    
    // MARK: - Search Bar
    
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索任务...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
                .onSubmit {
                    Task {
                        await taskService.searchTasks(searchText)
                    }
                }
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                    Task {
                        await taskService.fetchTasks()
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(10)
        .padding(.horizontal, 16)
        .padding(.top, 8)
    }
    
    // MARK: - Filter Chips
    
    private var filterChips: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(TaskFilter.allCases, id: \.self) { filter in
                    FilterChip(
                        title: filter.displayName,
                        isSelected: selectedFilter == filter,
                        count: getFilterCount(filter)
                    ) {
                        selectedFilter = filter
                        applyFilter(filter)
                    }
                }
            }
            .padding(.horizontal, 16)
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Task List
    
    private var taskList: some View {
        Group {
            if taskService.isLoading && taskService.tasks.isEmpty {
                LoadingView()
            } else if taskService.tasks.isEmpty {
                EmptyTasksView()
            } else {
                List {
                    ForEach(filteredTasks) { task in
                        TaskRowView(task: task) {
                            selectedTask = task
                        } onToggleComplete: {
                            Task {
                                try? await taskService.toggleTaskCompletion(task)
                            }
                        }
                        .listRowSeparator(.hidden)
                        .listRowInsets(EdgeInsets(top: 4, leading: 16, bottom: 4, trailing: 16))
                    }
                    .onDelete(perform: deleteTasks)
                }
                .listStyle(PlainListStyle())
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var filteredTasks: [Task] {
        switch selectedFilter {
        case .all:
            return taskService.tasks
        case .pending:
            return taskService.pendingTasks
        case .completed:
            return taskService.completedTasks
        case .overdue:
            return taskService.overdueTasks
        case .today:
            return taskService.todayTasks
        case .highPriority:
            return taskService.highPriorityTasks
        }
    }
    
    // MARK: - Helper Methods
    
    private func getFilterCount(_ filter: TaskFilter) -> Int {
        switch filter {
        case .all:
            return taskService.tasks.count
        case .pending:
            return taskService.pendingTasks.count
        case .completed:
            return taskService.completedTasks.count
        case .overdue:
            return taskService.overdueTasks.count
        case .today:
            return taskService.todayTasks.count
        case .highPriority:
            return taskService.highPriorityTasks.count
        }
    }
    
    private func applyFilter(_ filter: TaskFilter) {
        Task {
            switch filter {
            case .all:
                await taskService.fetchTasks()
            case .pending:
                await taskService.filterTasksByCompletion(false)
            case .completed:
                await taskService.filterTasksByCompletion(true)
            case .overdue:
                await taskService.fetchTasks() // 过期任务通过计算属性过滤
            case .today:
                await taskService.fetchTasks() // 今日任务通过计算属性过滤
            case .highPriority:
                await taskService.filterTasksByPriority(.high)
            }
        }
    }
    
    private func deleteTasks(offsets: IndexSet) {
        for index in offsets {
            let task = filteredTasks[index]
            Task {
                try? await taskService.deleteTask(task)
            }
        }
    }
}

// MARK: - Task Filter

enum TaskFilter: CaseIterable {
    case all
    case pending
    case completed
    case overdue
    case today
    case highPriority
    
    var displayName: String {
        switch self {
        case .all:
            return "全部"
        case .pending:
            return "待完成"
        case .completed:
            return "已完成"
        case .overdue:
            return "已过期"
        case .today:
            return "今日"
        case .highPriority:
            return "高优先级"
        }
    }
}

// MARK: - Filter Chip

struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let count: Int
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                if count > 0 {
                    Text("\(count)")
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(isSelected ? Color.white.opacity(0.3) : Color.accentColor.opacity(0.2))
                        .cornerRadius(8)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(isSelected ? Color.accentColor : Color(.systemGray5))
            .foregroundColor(isSelected ? .white : .primary)
            .cornerRadius(16)
        }
    }
}

// MARK: - Empty Tasks View

struct EmptyTasksView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "checkmark.circle")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text("暂无任务")
                .font(.title2)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Text("点击右上角的 + 按钮创建您的第一个任务")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.horizontal, 32)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Loading View

struct LoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("加载中...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    TasksView()
        .environmentObject(AppState())
}
