//
//  AddTaskView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct AddTaskView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var taskService = TaskService.shared
    
    @State private var title = ""
    @State private var description = ""
    @State private var priority: Task.Priority = .medium
    @State private var dueDate = Date()
    @State private var hasDueDate = false
    @State private var category = ""
    @State private var tags: [String] = []
    @State private var newTag = ""
    @State private var estimatedHours = 1
    @State private var estimatedMinutes = 0
    @State private var hasEstimatedDuration = false
    
    @State private var isLoading = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                // Basic Information
                basicInfoSection
                
                // Priority
                prioritySection
                
                // Due Date
                dueDateSection
                
                // Category and Tags
                categoryTagsSection
                
                // Estimated Duration
                estimatedDurationSection
            }
            .navigationTitle("新建任务")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveTask()
                    }
                    .disabled(!isFormValid || isLoading)
                }
            }
        }
        .alert("错误", isPresented: $showingError) {
            Button("确定") {}
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - Basic Information Section
    
    private var basicInfoSection: some View {
        Section("基本信息") {
            TextField("任务标题", text: $title)
                .textInputAutocapitalization(.sentences)
            
            TextField("任务描述（可选）", text: $description, axis: .vertical)
                .lineLimit(3...6)
                .textInputAutocapitalization(.sentences)
        }
    }
    
    // MARK: - Priority Section
    
    private var prioritySection: some View {
        Section("优先级") {
            Picker("优先级", selection: $priority) {
                ForEach(Task.Priority.allCases, id: \.self) { priority in
                    HStack {
                        Image(systemName: priority.iconName)
                            .foregroundColor(Color.fromColorName(priority.color))
                        Text(priority.displayName)
                    }
                    .tag(priority)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    
    // MARK: - Due Date Section
    
    private var dueDateSection: some View {
        Section("截止时间") {
            Toggle("设置截止时间", isOn: $hasDueDate)
            
            if hasDueDate {
                DatePicker(
                    "截止时间",
                    selection: $dueDate,
                    in: Date()...,
                    displayedComponents: [.date, .hourAndMinute]
                )
                .datePickerStyle(CompactDatePickerStyle())
            }
        }
    }
    
    // MARK: - Category and Tags Section
    
    private var categoryTagsSection: some View {
        Section("分类和标签") {
            TextField("分类（可选）", text: $category)
                .textInputAutocapitalization(.words)
            
            // Tags Input
            HStack {
                TextField("添加标签", text: $newTag)
                    .textInputAutocapitalization(.never)
                    .onSubmit {
                        addTag()
                    }
                
                Button("添加", action: addTag)
                    .disabled(newTag.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }
            
            // Tags Display
            if !tags.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack {
                        ForEach(tags, id: \.self) { tag in
                            TagView(tag: tag) {
                                removeTag(tag)
                            }
                        }
                    }
                    .padding(.horizontal, 4)
                }
            }
        }
    }
    
    // MARK: - Estimated Duration Section
    
    private var estimatedDurationSection: some View {
        Section("预估时长") {
            Toggle("设置预估时长", isOn: $hasEstimatedDuration)
            
            if hasEstimatedDuration {
                HStack {
                    Picker("小时", selection: $estimatedHours) {
                        ForEach(0...23, id: \.self) { hour in
                            Text("\(hour) 小时").tag(hour)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(maxWidth: .infinity)
                    
                    Picker("分钟", selection: $estimatedMinutes) {
                        ForEach([0, 15, 30, 45], id: \.self) { minute in
                            Text("\(minute) 分钟").tag(minute)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(maxWidth: .infinity)
                }
                .frame(height: 120)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    private var estimatedDuration: TimeInterval? {
        guard hasEstimatedDuration else { return nil }
        let totalMinutes = estimatedHours * 60 + estimatedMinutes
        return totalMinutes > 0 ? TimeInterval(totalMinutes * 60) : nil
    }
    
    // MARK: - Actions
    
    private func addTag() {
        let trimmedTag = newTag.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedTag.isEmpty && !tags.contains(trimmedTag) else { return }
        
        tags.append(trimmedTag)
        newTag = ""
    }
    
    private func removeTag(_ tag: String) {
        tags.removeAll { $0 == tag }
    }
    
    private func saveTask() {
        isLoading = true
        
        Task {
            do {
                try await taskService.createTask(
                    title: title.trimmingCharacters(in: .whitespacesAndNewlines),
                    description: description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : description.trimmingCharacters(in: .whitespacesAndNewlines),
                    priority: priority,
                    dueDate: hasDueDate ? dueDate : nil,
                    category: category.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : category.trimmingCharacters(in: .whitespacesAndNewlines),
                    tags: tags,
                    estimatedDuration: estimatedDuration
                )
                
                await MainActor.run {
                    isLoading = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }
    }
}

// MARK: - Tag View

struct TagView: View {
    let tag: String
    let onRemove: () -> Void
    
    var body: some View {
        HStack(spacing: 4) {
            Text("#\(tag)")
                .font(.caption)
                .foregroundColor(.accentColor)
            
            Button(action: onRemove) {
                Image(systemName: "xmark")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color.accentColor.opacity(0.1))
        .cornerRadius(8)
    }
}

#Preview {
    AddTaskView()
}
