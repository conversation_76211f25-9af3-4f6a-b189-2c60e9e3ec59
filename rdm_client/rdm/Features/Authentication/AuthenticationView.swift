//
//  AuthenticationView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI
import AuthenticationServices

struct AuthenticationView: View {
    @EnvironmentObject private var appState: AppState
    @State private var isSignUp = false
    @State private var email = ""
    @State private var password = ""
    @State private var username = ""
    @State private var confirmPassword = ""
    @State private var showingForgotPassword = false
    @State private var isPasswordVisible = false
    @State private var isConfirmPasswordVisible = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 32) {
                    // Logo and Title
                    headerSection
                    
                    // Form
                    formSection
                    
                    // Action Buttons
                    actionButtonsSection
                    
                    // Alternative Sign In
                    alternativeSignInSection
                    
                    // Toggle Sign Up/Sign In
                    toggleSection
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 32)
            }
            .navigationBarHidden(true)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(.systemBackground),
                        Color(.systemGray6)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
        }
        .sheet(isPresented: $showingForgotPassword) {
            ForgotPasswordView()
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            // App Icon
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.accentColor)
            
            // App Name
            Text("TaskFlow")
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            // Subtitle
            Text(isSignUp ? "创建您的账户" : "欢迎回来")
                .font(.title2)
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Form Section
    
    private var formSection: some View {
        VStack(spacing: 20) {
            // Username (Sign Up only)
            if isSignUp {
                CustomTextField(
                    title: "用户名",
                    text: $username,
                    placeholder: "请输入用户名",
                    icon: "person"
                )
                .textInputAutocapitalization(.never)
                .autocorrectionDisabled()
            }
            
            // Email
            CustomTextField(
                title: "邮箱",
                text: $email,
                placeholder: "请输入邮箱地址",
                icon: "envelope"
            )
            .keyboardType(.emailAddress)
            .textInputAutocapitalization(.never)
            .autocorrectionDisabled()
            
            // Password
            CustomSecureField(
                title: "密码",
                text: $password,
                placeholder: "请输入密码",
                icon: "lock",
                isVisible: $isPasswordVisible
            )
            
            // Confirm Password (Sign Up only)
            if isSignUp {
                CustomSecureField(
                    title: "确认密码",
                    text: $confirmPassword,
                    placeholder: "请再次输入密码",
                    icon: "lock",
                    isVisible: $isConfirmPasswordVisible
                )
            }
        }
    }
    
    // MARK: - Action Buttons Section
    
    private var actionButtonsSection: some View {
        VStack(spacing: 16) {
            // Primary Action Button
            Button(action: primaryAction) {
                HStack {
                    if appState.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Text(isSignUp ? "注册" : "登录")
                            .fontWeight(.semibold)
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(Color.accentColor)
                .foregroundColor(.white)
                .cornerRadius(12)
            }
            .disabled(!isFormValid || appState.isLoading)
            
            // Forgot Password (Sign In only)
            if !isSignUp {
                Button("忘记密码？") {
                    showingForgotPassword = true
                }
                .font(.subheadline)
                .foregroundColor(.accentColor)
            }
        }
    }
    
    // MARK: - Alternative Sign In Section
    
    private var alternativeSignInSection: some View {
        VStack(spacing: 16) {
            // Divider
            HStack {
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(.gray.opacity(0.3))
                
                Text("或")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 16)
                
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(.gray.opacity(0.3))
            }
            
            // Apple Sign In
            SignInWithAppleButton(
                onRequest: { request in
                    request.requestedScopes = [.fullName, .email]
                },
                onCompletion: { result in
                    handleAppleSignIn(result)
                }
            )
            .signInWithAppleButtonStyle(.black)
            .frame(height: 50)
            .cornerRadius(12)
        }
    }
    
    // MARK: - Toggle Section
    
    private var toggleSection: some View {
        HStack {
            Text(isSignUp ? "已有账户？" : "还没有账户？")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Button(isSignUp ? "立即登录" : "立即注册") {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isSignUp.toggle()
                    clearForm()
                }
            }
            .font(.subheadline)
            .fontWeight(.semibold)
            .foregroundColor(.accentColor)
        }
    }
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        let emailValid = !email.isEmpty && email.contains("@")
        let passwordValid = password.count >= 8
        
        if isSignUp {
            let usernameValid = username.count >= 3
            let passwordsMatch = password == confirmPassword
            return emailValid && passwordValid && usernameValid && passwordsMatch
        } else {
            return emailValid && passwordValid
        }
    }
    
    // MARK: - Actions
    
    private func primaryAction() {
        Task {
            if isSignUp {
                try await appState.signUp(email: email, password: password, username: username)
            } else {
                try await appState.signIn(email: email, password: password)
            }
        }
    }
    
    private func handleAppleSignIn(_ result: Result<ASAuthorization, Error>) {
        switch result {
        case .success:
            Task {
                try await appState.signInWithApple()
            }
        case .failure(let error):
            print("Apple Sign In failed: \(error)")
        }
    }
    
    private func clearForm() {
        email = ""
        password = ""
        username = ""
        confirmPassword = ""
        isPasswordVisible = false
        isConfirmPasswordVisible = false
    }
}

// MARK: - Custom Text Field

struct CustomTextField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    let icon: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.secondary)
                    .frame(width: 20)
                
                TextField(placeholder, text: $text)
                    .textFieldStyle(PlainTextFieldStyle())
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color.accentColor.opacity(text.isEmpty ? 0 : 0.5), lineWidth: 1)
            )
        }
    }
}

// MARK: - Custom Secure Field

struct CustomSecureField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    let icon: String
    @Binding var isVisible: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.secondary)
                    .frame(width: 20)
                
                if isVisible {
                    TextField(placeholder, text: $text)
                        .textFieldStyle(PlainTextFieldStyle())
                } else {
                    SecureField(placeholder, text: $text)
                        .textFieldStyle(PlainTextFieldStyle())
                }
                
                Button(action: { isVisible.toggle() }) {
                    Image(systemName: isVisible ? "eye.slash" : "eye")
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color.accentColor.opacity(text.isEmpty ? 0 : 0.5), lineWidth: 1)
            )
        }
    }
}

#Preview {
    AuthenticationView()
        .environmentObject(AppState())
}
