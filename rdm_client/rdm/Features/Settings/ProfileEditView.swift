//
//  ProfileEditView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct ProfileEditView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var appState: AppState
    @StateObject private var authService = AuthService.shared
    
    @State private var username: String
    @State private var email: String
    @State private var fullName: String
    @State private var timezone: String
    
    @State private var isLoading = false
    @State private var showingError = false
    @State private var errorMessage = ""
    @State private var showingChangePassword = false
    
    init() {
        let user = AuthService.shared.currentUser
        self._username = State(initialValue: user?.username ?? "")
        self._email = State(initialValue: user?.email ?? "")
        self._fullName = State(initialValue: user?.fullName ?? "")
        self._timezone = State(initialValue: user?.timezone ?? "UTC")
    }
    
    var body: some View {
        NavigationView {
            Form {
                // Profile Picture Section
                profilePictureSection
                
                // Basic Information
                basicInfoSection
                
                // Account Settings
                accountSettingsSection
                
                // Security
                securitySection
            }
            .navigationTitle("编辑资料")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveProfile()
                    }
                    .disabled(!hasChanges || isLoading)
                }
            }
        }
        .sheet(isPresented: $showingChangePassword) {
            ChangePasswordView()
        }
        .alert("错误", isPresented: $showingError) {
            Button("确定") {}
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - Profile Picture Section
    
    private var profilePictureSection: some View {
        Section {
            HStack {
                Spacer()
                
                VStack(spacing: 12) {
                    // Avatar
                    ZStack {
                        Circle()
                            .fill(Color.accentColor.opacity(0.1))
                            .frame(width: 80, height: 80)
                        
                        if let user = appState.currentUser {
                            Text(user.initials)
                                .font(.title)
                                .fontWeight(.semibold)
                                .foregroundColor(.accentColor)
                        } else {
                            Image(systemName: "person")
                                .font(.title)
                                .foregroundColor(.accentColor)
                        }
                    }
                    
                    Button("更换头像") {
                        // Handle avatar change
                    }
                    .font(.subheadline)
                    .foregroundColor(.accentColor)
                }
                
                Spacer()
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - Basic Information Section
    
    private var basicInfoSection: some View {
        Section("基本信息") {
            HStack {
                Text("用户名")
                    .frame(width: 80, alignment: .leading)
                TextField("用户名", text: $username)
                    .textInputAutocapitalization(.never)
                    .autocorrectionDisabled()
            }
            
            HStack {
                Text("邮箱")
                    .frame(width: 80, alignment: .leading)
                TextField("邮箱", text: $email)
                    .keyboardType(.emailAddress)
                    .textInputAutocapitalization(.never)
                    .autocorrectionDisabled()
            }
            
            HStack {
                Text("姓名")
                    .frame(width: 80, alignment: .leading)
                TextField("姓名", text: $fullName)
                    .textInputAutocapitalization(.words)
            }
        }
    }
    
    // MARK: - Account Settings Section
    
    private var accountSettingsSection: some View {
        Section("账户设置") {
            HStack {
                Text("时区")
                    .frame(width: 80, alignment: .leading)
                
                Picker("时区", selection: $timezone) {
                    Text("UTC").tag("UTC")
                    Text("Asia/Shanghai").tag("Asia/Shanghai")
                    Text("America/New_York").tag("America/New_York")
                    Text("Europe/London").tag("Europe/London")
                }
                .pickerStyle(MenuPickerStyle())
            }
            
            if let user = appState.currentUser {
                HStack {
                    Text("注册时间")
                        .frame(width: 80, alignment: .leading)
                    
                    Text(DateFormatter.mediumDate.string(from: user.createdAt))
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
                
                if let lastLogin = user.lastLoginAt {
                    HStack {
                        Text("最后登录")
                            .frame(width: 80, alignment: .leading)
                        
                        Text(DateFormatter.mediumDateTime.string(from: lastLogin))
                            .foregroundColor(.secondary)
                        
                        Spacer()
                    }
                }
                
                HStack {
                    Text("认证方式")
                        .frame(width: 80, alignment: .leading)
                    
                    if let authProvider = user.authProvider {
                        HStack(spacing: 4) {
                            Image(systemName: authProvider.iconName)
                                .font(.caption)
                            Text(authProvider.displayName)
                                .font(.subheadline)
                        }
                        .foregroundColor(.secondary)
                    } else {
                        Text("邮箱")
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                }
            }
        }
    }
    
    // MARK: - Security Section
    
    private var securitySection: some View {
        Section("安全") {
            if appState.currentUser?.authProvider == .email || appState.currentUser?.authProvider == nil {
                Button(action: { showingChangePassword = true }) {
                    HStack {
                        Image(systemName: "key")
                            .foregroundColor(.blue)
                            .frame(width: 24)
                        
                        Text("修改密码")
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            HStack {
                Image(systemName: "checkmark.shield")
                    .foregroundColor(appState.currentUser?.isEmailVerified == true ? .green : .orange)
                    .frame(width: 24)
                
                Text("邮箱验证")
                
                Spacer()
                
                if appState.currentUser?.isEmailVerified == true {
                    Text("已验证")
                        .foregroundColor(.green)
                        .font(.subheadline)
                } else {
                    Button("验证") {
                        // Handle email verification
                    }
                    .font(.subheadline)
                    .foregroundColor(.accentColor)
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var hasChanges: Bool {
        guard let user = appState.currentUser else { return false }
        
        return username != user.username ||
               email != user.email ||
               fullName != (user.fullName ?? "") ||
               timezone != (user.timezone ?? "UTC")
    }
    
    // MARK: - Actions
    
    private func saveProfile() {
        isLoading = true
        errorMessage = ""
        
        let updateRequest = UserUpdateRequest(
            username: username.trimmingCharacters(in: .whitespacesAndNewlines),
            email: email.trimmingCharacters(in: .whitespacesAndNewlines),
            fullName: fullName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : fullName.trimmingCharacters(in: .whitespacesAndNewlines),
            timezone: timezone
        )
        
        Task {
            do {
                try await authService.updateUser(updateRequest)
                
                await MainActor.run {
                    isLoading = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }
    }
}

// MARK: - Date Formatters

extension DateFormatter {
    static let mediumDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }()
}

#Preview {
    ProfileEditView()
        .environmentObject(AppState())
}
