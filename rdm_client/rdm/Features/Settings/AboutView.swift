//
//  AboutView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 32) {
                    // App Icon and Info
                    appInfoSection
                    
                    // Features
                    featuresSection
                    
                    // Team
                    teamSection
                    
                    // Legal
                    legalSection
                    
                    // Contact
                    contactSection
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("关于")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - App Info Section
    
    private var appInfoSection: some View {
        VStack(spacing: 16) {
            // App Icon
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 100, height: 100)
                .overlay(
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.white)
                )
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
            
            // App Name and Version
            VStack(spacing: 8) {
                Text("TaskFlow")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("版本 1.0.0")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text("高效的任务管理和习惯追踪应用")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    // MARK: - Features Section
    
    private var featuresSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("主要功能")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                FeatureRow(
                    icon: "checkmark.circle",
                    title: "任务管理",
                    description: "创建、编辑和跟踪您的任务，设置优先级和截止时间"
                )
                
                FeatureRow(
                    icon: "target",
                    title: "习惯追踪",
                    description: "建立良好习惯，记录进度，查看连续天数统计"
                )
                
                FeatureRow(
                    icon: "chart.bar",
                    title: "数据统计",
                    description: "可视化您的生产力数据，了解工作和生活模式"
                )
                
                FeatureRow(
                    icon: "icloud",
                    title: "云端同步",
                    description: "数据安全存储在云端，多设备无缝同步"
                )
                
                FeatureRow(
                    icon: "bell",
                    title: "智能提醒",
                    description: "任务截止提醒和习惯执行提醒，不错过重要事项"
                )
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Team Section
    
    private var teamSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("开发团队")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                TeamMemberRow(
                    name: "TaskFlow Team",
                    role: "产品设计与开发",
                    description: "致力于创造简洁高效的生产力工具"
                )
            }
            
            Text("感谢所有为TaskFlow贡献想法和反馈的用户！")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .italic()
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Legal Section
    
    private var legalSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("法律信息")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                Button("隐私政策") {
                    // Handle privacy policy
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .foregroundColor(.accentColor)
                
                Button("服务条款") {
                    // Handle terms of service
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .foregroundColor(.accentColor)
                
                Button("开源许可") {
                    // Handle open source licenses
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .foregroundColor(.accentColor)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Contact Section
    
    private var contactSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("联系我们")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                ContactRow(
                    icon: "envelope",
                    title: "邮箱",
                    value: "<EMAIL>",
                    action: {
                        if let url = URL(string: "mailto:<EMAIL>") {
                            UIApplication.shared.open(url)
                        }
                    }
                )
                
                ContactRow(
                    icon: "globe",
                    title: "官网",
                    value: "www.taskflow.app",
                    action: {
                        if let url = URL(string: "https://www.taskflow.app") {
                            UIApplication.shared.open(url)
                        }
                    }
                )
                
                ContactRow(
                    icon: "message",
                    title: "反馈",
                    value: "意见建议",
                    action: {
                        // Handle feedback
                    }
                )
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

// MARK: - Feature Row

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.accentColor)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            Spacer()
        }
    }
}

// MARK: - Team Member Row

struct TeamMemberRow: View {
    let name: String
    let role: String
    let description: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Circle()
                    .fill(Color.accentColor.opacity(0.1))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Text(String(name.prefix(1)))
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.accentColor)
                    )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Text(role)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Contact Row

struct ContactRow: View {
    let icon: String
    let title: String
    let value: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.subheadline)
                    .foregroundColor(.accentColor)
                    .frame(width: 20)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(value)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

#Preview {
    AboutView()
}
