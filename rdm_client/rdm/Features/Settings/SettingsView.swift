//
//  SettingsView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct SettingsView: View {
    @EnvironmentObject private var appState: AppState
    @StateObject private var authService = AuthService.shared
    @State private var showingProfileEdit = false
    @State private var showingNotificationSettings = false
    @State private var showingAbout = false
    @State private var showingSignOutAlert = false
    
    var body: some View {
        NavigationView {
            List {
                // User Profile Section
                userProfileSection
                
                // Preferences Section
                preferencesSection
                
                // Notifications Section
                notificationsSection
                
                // Data & Privacy Section
                dataPrivacySection
                
                // Support Section
                supportSection
                
                // Account Section
                accountSection
            }
            .navigationTitle("设置")
        }
        .sheet(isPresented: $showingProfileEdit) {
            ProfileEditView()
        }
        .sheet(isPresented: $showingNotificationSettings) {
            NotificationSettingsView()
        }
        .sheet(isPresented: $showingAbout) {
            AboutView()
        }
        .alert("退出登录", isPresented: $showingSignOutAlert) {
            Button("取消", role: .cancel) {}
            But<PERSON>("退出", role: .destructive) {
                Task {
                    try await appState.signOut()
                }
            }
        } message: {
            Text("确定要退出登录吗？")
        }
    }
    
    // MARK: - User Profile Section
    
    private var userProfileSection: some View {
        Section {
            HStack(spacing: 16) {
                // Avatar
                ZStack {
                    Circle()
                        .fill(Color.accentColor.opacity(0.1))
                        .frame(width: 60, height: 60)
                    
                    if let user = appState.currentUser {
                        Text(user.initials)
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.accentColor)
                    } else {
                        Image(systemName: "person")
                            .font(.title2)
                            .foregroundColor(.accentColor)
                    }
                }
                
                // User Info
                VStack(alignment: .leading, spacing: 4) {
                    if let user = appState.currentUser {
                        Text(user.displayName)
                            .font(.headline)
                            .fontWeight(.medium)
                        
                        Text(user.email)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        if let authProvider = user.authProvider {
                            HStack(spacing: 4) {
                                Image(systemName: authProvider.iconName)
                                    .font(.caption)
                                Text(authProvider.displayName)
                                    .font(.caption)
                            }
                            .foregroundColor(.secondary)
                        }
                    } else {
                        Text("未登录")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Button(action: { showingProfileEdit = true }) {
                    Image(systemName: "pencil")
                        .foregroundColor(.accentColor)
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - Preferences Section
    
    private var preferencesSection: some View {
        Section("偏好设置") {
            // Theme Setting
            HStack {
                Image(systemName: "paintbrush")
                    .foregroundColor(.blue)
                    .frame(width: 24)
                
                Text("主题")
                
                Spacer()
                
                Picker("主题", selection: Binding(
                    get: { appState.appTheme },
                    set: { appState.updateTheme($0) }
                )) {
                    ForEach(AppTheme.allCases, id: \.self) { theme in
                        Text(theme.displayName).tag(theme)
                    }
                }
                .pickerStyle(MenuPickerStyle())
            }
            
            // Language Setting (placeholder)
            HStack {
                Image(systemName: "globe")
                    .foregroundColor(.green)
                    .frame(width: 24)
                
                Text("语言")
                
                Spacer()
                
                Text("简体中文")
                    .foregroundColor(.secondary)
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // Week Start Setting
            HStack {
                Image(systemName: "calendar")
                    .foregroundColor(.orange)
                    .frame(width: 24)
                
                Text("一周开始")
                
                Spacer()
                
                Text("周一")
                    .foregroundColor(.secondary)
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - Notifications Section
    
    private var notificationsSection: some View {
        Section("通知") {
            Button(action: { showingNotificationSettings = true }) {
                HStack {
                    Image(systemName: "bell")
                        .foregroundColor(.purple)
                        .frame(width: 24)
                    
                    Text("通知设置")
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    // MARK: - Data & Privacy Section
    
    private var dataPrivacySection: some View {
        Section("数据与隐私") {
            SettingsRow(
                icon: "icloud",
                iconColor: .blue,
                title: "数据同步",
                subtitle: "自动同步到云端"
            ) {
                // Handle data sync settings
            }
            
            SettingsRow(
                icon: "lock.shield",
                iconColor: .green,
                title: "隐私设置",
                subtitle: "管理数据隐私"
            ) {
                // Handle privacy settings
            }
            
            SettingsRow(
                icon: "square.and.arrow.down",
                iconColor: .orange,
                title: "导出数据",
                subtitle: "导出您的数据"
            ) {
                // Handle data export
            }
        }
    }
    
    // MARK: - Support Section
    
    private var supportSection: some View {
        Section("支持") {
            SettingsRow(
                icon: "questionmark.circle",
                iconColor: .blue,
                title: "帮助中心",
                subtitle: "常见问题和使用指南"
            ) {
                // Handle help center
            }
            
            SettingsRow(
                icon: "envelope",
                iconColor: .green,
                title: "联系我们",
                subtitle: "反馈问题和建议"
            ) {
                // Handle contact
            }
            
            SettingsRow(
                icon: "star",
                iconColor: .yellow,
                title: "评价应用",
                subtitle: "在App Store中评价"
            ) {
                // Handle app rating
            }
            
            Button(action: { showingAbout = true }) {
                HStack {
                    Image(systemName: "info.circle")
                        .foregroundColor(.gray)
                        .frame(width: 24)
                    
                    Text("关于")
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    // MARK: - Account Section
    
    private var accountSection: some View {
        Section("账户") {
            Button(action: { showingSignOutAlert = true }) {
                HStack {
                    Image(systemName: "rectangle.portrait.and.arrow.right")
                        .foregroundColor(.red)
                        .frame(width: 24)
                    
                    Text("退出登录")
                        .foregroundColor(.red)
                }
            }
            
            Button(action: {}) {
                HStack {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                        .frame(width: 24)
                    
                    Text("删除账户")
                        .foregroundColor(.red)
                }
            }
        }
    }
}

// MARK: - Settings Row

struct SettingsRow: View {
    let icon: String
    let iconColor: Color
    let title: String
    let subtitle: String?
    let action: () -> Void
    
    init(
        icon: String,
        iconColor: Color,
        title: String,
        subtitle: String? = nil,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.iconColor = iconColor
        self.title = title
        self.subtitle = subtitle
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(iconColor)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .foregroundColor(.primary)
                    
                    if let subtitle = subtitle {
                        Text(subtitle)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

#Preview {
    SettingsView()
        .environmentObject(AppState())
}
