//
//  NotificationSettingsView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI
import UserNotifications

struct NotificationSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var appState: AppState
    
    @State private var isNotificationsEnabled = false
    @State private var taskReminders = true
    @State private var habitReminders = true
    @State private var dailySummary = true
    @State private var weeklyReport = false
    @State private var reminderTime = Date()
    @State private var summaryTime = Date()
    
    @State private var showingPermissionAlert = false
    
    var body: some View {
        NavigationView {
            Form {
                // Notification Status
                notificationStatusSection
                
                // Task Notifications
                if isNotificationsEnabled {
                    taskNotificationsSection
                    
                    // Habit Notifications
                    habitNotificationsSection
                    
                    // Summary Notifications
                    summaryNotificationsSection
                    
                    // Timing Settings
                    timingSettingsSection
                }
            }
            .navigationTitle("通知设置")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("完成") {
                        dismiss()
                    }
                }
            }
        }
        .alert("通知权限", isPresented: $showingPermissionAlert) {
            Button("取消", role: .cancel) {}
            Button("去设置") {
                openAppSettings()
            }
        } message: {
            Text("请在设置中允许TaskFlow发送通知，以便接收任务和习惯提醒。")
        }
        .onAppear {
            checkNotificationPermission()
        }
    }
    
    // MARK: - Notification Status Section
    
    private var notificationStatusSection: some View {
        Section {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: isNotificationsEnabled ? "bell.fill" : "bell.slash")
                        .foregroundColor(isNotificationsEnabled ? .green : .red)
                        .font(.title2)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("通知状态")
                            .font(.headline)
                            .fontWeight(.medium)
                        
                        Text(isNotificationsEnabled ? "已启用" : "已禁用")
                            .font(.subheadline)
                            .foregroundColor(isNotificationsEnabled ? .green : .red)
                    }
                    
                    Spacer()
                    
                    if !isNotificationsEnabled {
                        Button("启用") {
                            requestNotificationPermission()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
                
                if !isNotificationsEnabled {
                    Text("启用通知后，您将收到任务提醒、习惯提醒和每日总结。")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - Task Notifications Section
    
    private var taskNotificationsSection: some View {
        Section("任务提醒") {
            Toggle("任务截止提醒", isOn: $taskReminders)
            
            if taskReminders {
                VStack(alignment: .leading, spacing: 8) {
                    Text("提醒时机")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    VStack(spacing: 4) {
                        HStack {
                            Image(systemName: "clock")
                                .foregroundColor(.orange)
                                .frame(width: 20)
                            Text("截止前1小时")
                            Spacer()
                            Toggle("", isOn: .constant(true))
                                .labelsHidden()
                        }
                        
                        HStack {
                            Image(systemName: "clock")
                                .foregroundColor(.orange)
                                .frame(width: 20)
                            Text("截止前1天")
                            Spacer()
                            Toggle("", isOn: .constant(true))
                                .labelsHidden()
                        }
                        
                        HStack {
                            Image(systemName: "exclamationmark.triangle")
                                .foregroundColor(.red)
                                .frame(width: 20)
                            Text("已过期提醒")
                            Spacer()
                            Toggle("", isOn: .constant(false))
                                .labelsHidden()
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Habit Notifications Section
    
    private var habitNotificationsSection: some View {
        Section("习惯提醒") {
            Toggle("习惯执行提醒", isOn: $habitReminders)
            
            if habitReminders {
                VStack(alignment: .leading, spacing: 8) {
                    Text("提醒方式")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    VStack(spacing: 4) {
                        HStack {
                            Image(systemName: "bell")
                                .foregroundColor(.blue)
                                .frame(width: 20)
                            Text("按习惯设定时间")
                            Spacer()
                            Toggle("", isOn: .constant(true))
                                .labelsHidden()
                        }
                        
                        HStack {
                            Image(systemName: "clock.arrow.circlepath")
                                .foregroundColor(.purple)
                                .frame(width: 20)
                            Text("连续天数提醒")
                            Spacer()
                            Toggle("", isOn: .constant(true))
                                .labelsHidden()
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Summary Notifications Section
    
    private var summaryNotificationsSection: some View {
        Section("总结报告") {
            Toggle("每日总结", isOn: $dailySummary)
            Toggle("每周报告", isOn: $weeklyReport)
            
            if dailySummary || weeklyReport {
                Text("总结内容包括任务完成情况、习惯执行情况和生产力统计。")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - Timing Settings Section
    
    private var timingSettingsSection: some View {
        Section("时间设置") {
            if taskReminders {
                DatePicker(
                    "默认提醒时间",
                    selection: $reminderTime,
                    displayedComponents: .hourAndMinute
                )
                .datePickerStyle(CompactDatePickerStyle())
            }
            
            if dailySummary {
                DatePicker(
                    "每日总结时间",
                    selection: $summaryTime,
                    displayedComponents: .hourAndMinute
                )
                .datePickerStyle(CompactDatePickerStyle())
            }
        }
    }
    
    // MARK: - Actions
    
    private func checkNotificationPermission() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                isNotificationsEnabled = settings.authorizationStatus == .authorized
            }
        }
    }
    
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                if granted {
                    isNotificationsEnabled = true
                } else {
                    showingPermissionAlert = true
                }
            }
        }
    }
    
    private func openAppSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}

#Preview {
    NotificationSettingsView()
        .environmentObject(AppState())
}
