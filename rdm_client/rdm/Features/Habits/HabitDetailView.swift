//
//  HabitDetailView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct HabitDetailView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var habitService = HabitService.shared
    
    @State private var habit: Habit
    @State private var showingEditView = false
    @State private var showingDeleteAlert = false
    @State private var showingRecordView = false
    @State private var isLoading = false
    
    init(habit: Habit) {
        self._habit = State(initialValue: habit)
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Statistics Cards
                    statisticsSection
                    
                    // Progress Chart
                    progressChartSection
                    
                    // Habit Information
                    habitInfoSection
                    
                    // Actions
                    actionsSection
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("习惯详情")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("编辑") {
                        showingEditView = true
                    }
                }
            }
        }
        .sheet(isPresented: $showingEditView) {
            EditHabitView(habit: habit) { updatedHabit in
                habit = updatedHabit
            }
        }
        .sheet(isPresented: $showingRecordView) {
            HabitRecordView(habit: habit)
        }
        .alert("删除习惯", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) {}
            Button("删除", role: .destructive) {
                deleteHabit()
            }
        } message: {
            Text("确定要删除这个习惯吗？此操作无法撤销。")
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Icon and Color
            ZStack {
                Circle()
                    .fill(habitColor.opacity(0.1))
                    .frame(width: 80, height: 80)
                
                Image(systemName: habit.icon ?? "target")
                    .font(.system(size: 32))
                    .foregroundColor(habitColor)
            }
            
            // Name and Description
            VStack(spacing: 8) {
                Text(habit.name)
                    .font(.title2)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                if let description = habit.description, !description.isEmpty {
                    Text(description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            
            // Status Badge
            HStack(spacing: 12) {
                StatusBadge(
                    text: habit.frequency.displayName,
                    color: habitColor,
                    icon: habit.frequency.iconName
                )
                
                StatusBadge(
                    text: habit.statusDescription,
                    color: habit.isActive ? .green : .gray,
                    icon: habit.isActive ? "play.circle" : "pause.circle"
                )
            }
        }
    }
    
    // MARK: - Statistics Section
    
    private var statisticsSection: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
            StatCard(
                title: "当前连续",
                value: "\(habit.currentStreak)",
                unit: "天",
                icon: "flame",
                color: .orange
            )
            
            StatCard(
                title: "最佳记录",
                value: "\(habit.bestStreak)",
                unit: "天",
                icon: "trophy",
                color: .yellow
            )
            
            StatCard(
                title: "总完成次数",
                value: "\(habit.totalCompletions)",
                unit: "次",
                icon: "checkmark.circle",
                color: .green
            )
            
            StatCard(
                title: "目标值",
                value: "\(habit.targetValue)",
                unit: habit.unit ?? "",
                icon: "target",
                color: habitColor
            )
        }
    }
    
    // MARK: - Progress Chart Section
    
    private var progressChartSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("最近7天")
                .font(.headline)
                .fontWeight(.semibold)
            
            // Simple progress chart (placeholder)
            HStack(spacing: 8) {
                ForEach(0..<7, id: \.self) { index in
                    VStack(spacing: 4) {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(index < 4 ? habitColor : Color(.systemGray5))
                            .frame(width: 32, height: 40)
                        
                        Text(dayAbbreviation(for: index))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .frame(maxWidth: .infinity)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - Habit Information Section
    
    private var habitInfoSection: some View {
        VStack(spacing: 12) {
            if let startDate = habit.startDate {
                InfoRow(
                    icon: "calendar",
                    title: "开始日期",
                    value: DateFormatter.mediumDate.string(from: startDate),
                    color: .blue
                )
            }
            
            if let endDate = habit.endDate {
                InfoRow(
                    icon: "calendar.badge.clock",
                    title: "结束日期",
                    value: DateFormatter.mediumDate.string(from: endDate),
                    color: .orange
                )
            }
            
            if let reminderTime = habit.reminderTime, habit.isReminderEnabled {
                InfoRow(
                    icon: "bell",
                    title: "提醒时间",
                    value: "每日 \(reminderTime)",
                    color: .purple
                )
            }
            
            InfoRow(
                icon: "clock",
                title: "创建时间",
                value: DateFormatter.mediumDate.string(from: habit.createdAt),
                color: .gray
            )
        }
    }
    
    // MARK: - Actions Section
    
    private var actionsSection: some View {
        VStack(spacing: 12) {
            if habit.canRecord {
                Button(action: { showingRecordView = true }) {
                    HStack {
                        Image(systemName: "plus.circle")
                        Text("记录完成")
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(habitColor)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
            }
            
            Button(action: toggleActive) {
                HStack {
                    Image(systemName: habit.isActive ? "pause.circle" : "play.circle")
                    Text(habit.isActive ? "暂停习惯" : "恢复习惯")
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(habit.isActive ? Color.orange : Color.green)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
            .disabled(isLoading)
            
            Button(action: { showingDeleteAlert = true }) {
                HStack {
                    Image(systemName: "trash")
                    Text("删除习惯")
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.red)
                .foregroundColor(.white)
                .cornerRadius(10)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var habitColor: Color {
        if let colorString = habit.color {
            return Color.fromHexOrName(colorString)
        } else {
            return .accentColor
        }
    }
    
    // MARK: - Helper Methods
    
    private func dayAbbreviation(for index: Int) -> String {
        let calendar = Calendar.current
        let date = calendar.date(byAdding: .day, value: index - 6, to: Date()) ?? Date()
        let formatter = DateFormatter()
        formatter.dateFormat = "E"
        return formatter.string(from: date)
    }
    
    // MARK: - Actions
    
    private func toggleActive() {
        isLoading = true
        
        Task {
            do {
                let updatedHabit = try await habitService.toggleHabitActive(habit)
                await MainActor.run {
                    habit = updatedHabit
                    isLoading = false
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                }
            }
        }
    }
    
    private func deleteHabit() {
        Task {
            do {
                try await habitService.deleteHabit(habit)
                await MainActor.run {
                    dismiss()
                }
            } catch {
                // Handle error
            }
        }
    }
}

// MARK: - Status Badge

struct StatusBadge: View {
    let text: String
    let color: Color
    let icon: String
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption)
            Text(text)
                .font(.caption)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(color.opacity(0.1))
        .foregroundColor(color)
        .cornerRadius(8)
    }
}

// MARK: - Stat Card

struct StatCard: View {
    let title: String
    let value: String
    let unit: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            VStack(spacing: 2) {
                HStack(alignment: .bottom, spacing: 2) {
                    Text(value)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    if !unit.isEmpty {
                        Text(unit)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

#Preview {
    HabitDetailView(habit: Habit.preview())
}
