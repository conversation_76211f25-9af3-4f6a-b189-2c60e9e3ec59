//
//  HabitRowView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct HabitRowView: View {
    let habit: Habit
    let onTap: () -> Void
    let onToggleRecord: () -> Void
    
    @StateObject private var habitService = HabitService.shared
    @State private var isRecording = false
    
    var body: some View {
        HStack(spacing: 12) {
            // Habit Icon
            habitIcon
            
            // Habit Content
            VStack(alignment: .leading, spacing: 4) {
                // Name and Status
                nameSection
                
                // Description
                if let description = habit.description, !description.isEmpty {
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                // Metadata
                metadataSection
            }
            
            Spacer()
            
            // Right Section
            rightSection
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(habit.isActive ? Color(.systemBackground) : Color(.systemGray6))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(borderColor, lineWidth: 1)
        )
        .opacity(habit.isActive ? 1.0 : 0.7)
        .onTapGesture {
            onTap()
        }
    }
    
    // MARK: - Habit Icon
    
    private var habitIcon: some View {
        ZStack {
            Circle()
                .fill(habitColor.opacity(0.1))
                .frame(width: 40, height: 40)
            
            Image(systemName: habit.icon ?? "target")
                .font(.system(size: 18))
                .foregroundColor(habitColor)
        }
    }
    
    // MARK: - Name Section
    
    private var nameSection: some View {
        HStack(spacing: 8) {
            Text(habit.name)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(habit.isActive ? .primary : .secondary)
            
            // Frequency Badge
            Text(habit.frequency.displayName)
                .font(.caption2)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(habitColor.opacity(0.1))
                .foregroundColor(habitColor)
                .cornerRadius(4)
            
            if !habit.isActive {
                Text("已暂停")
                    .font(.caption2)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.gray.opacity(0.1))
                    .foregroundColor(.gray)
                    .cornerRadius(4)
            }
        }
    }
    
    // MARK: - Metadata Section
    
    private var metadataSection: some View {
        HStack(spacing: 12) {
            // Target Value
            if habit.type != .boolean {
                Label(habit.targetDescription, systemImage: "target")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            // Current Streak
            if habit.currentStreak > 0 {
                Label("\(habit.currentStreak)天", systemImage: "flame")
                    .font(.caption2)
                    .foregroundColor(.orange)
            }
            
            Spacer()
        }
    }
    
    // MARK: - Right Section
    
    private var rightSection: some View {
        VStack(alignment: .trailing, spacing: 8) {
            // Record Button
            recordButton
            
            // Statistics
            statisticsView
        }
    }
    
    // MARK: - Record Button
    
    private var recordButton: some View {
        Button(action: toggleRecord) {
            ZStack {
                Circle()
                    .stroke(habitColor.opacity(0.3), lineWidth: 2)
                    .frame(width: 32, height: 32)
                
                if isCompletedToday {
                    Image(systemName: "checkmark")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(habitColor)
                } else if isRecording {
                    ProgressView()
                        .scaleEffect(0.7)
                        .progressViewStyle(CircularProgressViewStyle(tint: habitColor))
                } else {
                    Circle()
                        .fill(habitColor.opacity(0.1))
                        .frame(width: 20, height: 20)
                }
            }
        }
        .disabled(isRecording || !habit.canRecord)
    }
    
    // MARK: - Statistics View
    
    private var statisticsView: some View {
        VStack(alignment: .trailing, spacing: 2) {
            if habit.bestStreak > 0 {
                Text("最佳 \(habit.bestStreak)天")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            if habit.totalCompletions > 0 {
                Text("共 \(habit.totalCompletions)次")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var habitColor: Color {
        if let colorString = habit.color {
            return Color.fromHexOrName(colorString)
        } else {
            return .accentColor
        }
    }
    
    private var borderColor: Color {
        if isCompletedToday {
            return habitColor.opacity(0.3)
        } else {
            return .clear
        }
    }
    
    private var isCompletedToday: Bool {
        habitService.isHabitCompletedToday(habit.id)
    }
    
    // MARK: - Actions
    
    private func toggleRecord() {
        isRecording = true
        
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // 延迟执行以显示动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            onToggleRecord()
            isRecording = false
        }
    }
}



#Preview {
    VStack(spacing: 16) {
        HabitRowView(
            habit: Habit.preview(),
            onTap: {},
            onToggleRecord: {}
        )
        
        HabitRowView(
            habit: Habit.readingPreview(),
            onTap: {},
            onToggleRecord: {}
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
