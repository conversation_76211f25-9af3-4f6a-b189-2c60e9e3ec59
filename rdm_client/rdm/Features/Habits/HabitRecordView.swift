//
//  HabitRecordView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct HabitRecordView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var habitService = HabitService.shared
    
    let habit: Habit
    
    @State private var recordValue: Double = 1
    @State private var recordDate = Date()
    @State private var notes = ""
    @State private var isLoading = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                // Habit Info
                habitInfoSection
                
                // Record Value
                if habit.type != .boolean {
                    recordValueSection
                }
                
                // Date and Time
                dateTimeSection
                
                // Notes
                notesSection
            }
            .navigationTitle("记录习惯")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("保存") {
                        recordHabit()
                    }
                    .disabled(isLoading)
                }
            }
        }
        .alert("错误", isPresented: $showingError) {
            But<PERSON>("确定") {}
        } message: {
            Text(errorMessage)
        }
        .onAppear {
            setupInitialValues()
        }
    }
    
    // MARK: - Habit Info Section
    
    private var habitInfoSection: some View {
        Section {
            HStack(spacing: 16) {
                // Icon
                ZStack {
                    Circle()
                        .fill(habitColor.opacity(0.1))
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: habit.icon ?? "target")
                        .font(.title2)
                        .foregroundColor(habitColor)
                }
                
                // Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(habit.name)
                        .font(.headline)
                        .fontWeight(.medium)
                    
                    if let description = habit.description, !description.isEmpty {
                        Text(description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                    
                    HStack(spacing: 8) {
                        Text(habit.frequency.displayName)
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(habitColor.opacity(0.1))
                            .foregroundColor(habitColor)
                            .cornerRadius(4)
                        
                        Text(habit.targetDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - Record Value Section
    
    private var recordValueSection: some View {
        Section("记录值") {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("完成量")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text(formattedRecordValue)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(habitColor)
                }
                
                if habit.type == .duration {
                    // Duration picker
                    HStack {
                        Text("小时")
                        Picker("小时", selection: Binding(
                            get: { Int(recordValue) / 60 },
                            set: { recordValue = Double($0 * 60 + Int(recordValue) % 60) }
                        )) {
                            ForEach(0...23, id: \.self) { hour in
                                Text("\(hour)").tag(hour)
                            }
                        }
                        .pickerStyle(WheelPickerStyle())
                        .frame(maxWidth: .infinity)
                        
                        Text("分钟")
                        Picker("分钟", selection: Binding(
                            get: { Int(recordValue) % 60 },
                            set: { recordValue = Double((Int(recordValue) / 60) * 60 + $0) }
                        )) {
                            ForEach([0, 15, 30, 45], id: \.self) { minute in
                                Text("\(minute)").tag(minute)
                            }
                        }
                        .pickerStyle(WheelPickerStyle())
                        .frame(maxWidth: .infinity)
                    }
                    .frame(height: 120)
                } else {
                    // Numeric value
                    HStack {
                        Button(action: { decrementValue() }) {
                            Image(systemName: "minus.circle")
                                .font(.title2)
                                .foregroundColor(habitColor)
                        }
                        .disabled(recordValue <= 0)
                        
                        Spacer()
                        
                        TextField("值", value: $recordValue, format: .number)
                            .keyboardType(.decimalPad)
                            .multilineTextAlignment(.center)
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(habitColor)
                        
                        Spacer()
                        
                        Button(action: { incrementValue() }) {
                            Image(systemName: "plus.circle")
                                .font(.title2)
                                .foregroundColor(habitColor)
                        }
                    }
                    .padding(.vertical, 8)
                }
                
                // Progress towards target
                if habit.targetValue > 0 {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("目标进度")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text("\(Int(progressPercentage))%")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(habitColor)
                        }
                        
                        ProgressView(value: progressPercentage / 100)
                            .progressViewStyle(LinearProgressViewStyle(tint: habitColor))
                            .scaleEffect(x: 1, y: 2, anchor: .center)
                    }
                }
            }
        }
    }
    
    // MARK: - Date Time Section
    
    private var dateTimeSection: some View {
        Section("时间") {
            DatePicker(
                "记录时间",
                selection: $recordDate,
                in: ...Date(),
                displayedComponents: [.date, .hourAndMinute]
            )
            .datePickerStyle(CompactDatePickerStyle())
        }
    }
    
    // MARK: - Notes Section
    
    private var notesSection: some View {
        Section("备注") {
            TextField("添加备注（可选）", text: $notes, axis: .vertical)
                .lineLimit(3...6)
                .textInputAutocapitalization(.sentences)
        }
    }
    
    // MARK: - Computed Properties
    
    private var habitColor: Color {
        if let colorString = habit.color {
            return Color.fromHexOrName(colorString)
        } else {
            return .accentColor
        }
    }
    
    private var formattedRecordValue: String {
        switch habit.type {
        case .boolean:
            return "完成"
        case .duration:
            let hours = Int(recordValue) / 60
            let minutes = Int(recordValue) % 60
            if hours > 0 {
                return "\(hours)小时\(minutes)分钟"
            } else {
                return "\(minutes)分钟"
            }
        case .numeric:
            if let unit = habit.unit, !unit.isEmpty {
                return "\(Int(recordValue)) \(unit)"
            } else {
                return "\(Int(recordValue))"
            }
        }
    }
    
    private var progressPercentage: Double {
        guard habit.targetValue > 0 else { return 0 }
        return min(100, (recordValue / Double(habit.targetValue)) * 100)
    }
    
    // MARK: - Actions
    
    private func setupInitialValues() {
        recordValue = Double(habit.targetValue)
    }
    
    private func incrementValue() {
        switch habit.type {
        case .duration:
            recordValue += 15 // 15 minutes
        case .numeric:
            recordValue += 1
        case .boolean:
            break
        }
    }
    
    private func decrementValue() {
        switch habit.type {
        case .duration:
            recordValue = max(0, recordValue - 15)
        case .numeric:
            recordValue = max(0, recordValue - 1)
        case .boolean:
            break
        }
    }
    
    private func recordHabit() {
        isLoading = true
        
        Task {
            do {
                try await habitService.recordHabitCompletion(
                    habitId: habit.id,
                    value: recordValue,
                    date: recordDate,
                    notes: notes.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : notes.trimmingCharacters(in: .whitespacesAndNewlines)
                )
                
                await MainActor.run {
                    isLoading = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }
    }
}

#Preview {
    HabitRecordView(habit: Habit.preview())
}
