//
//  HabitsView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct HabitsView: View {
    @StateObject private var habitService = HabitService.shared
    @State private var showingAddHabit = false
    @State private var selectedHabit: Habit?
    @State private var showingFilterSheet = false
    @State private var selectedFilter: HabitFilter = .all
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Today's Progress
                todayProgressSection
                
                // Filter Chips
                filterChips
                
                // Habits List
                habitsList
            }
            .navigationTitle("习惯")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { showingFilterSheet = true }) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingAddHabit = true }) {
                        Image(systemName: "plus")
                    }
                }
            }
            .refreshable {
                await habitService.refreshHabits()
            }
        }
        .sheet(isPresented: $showingAddHabit) {
            AddHabitView()
        }
        .sheet(isPresented: $showingFilterSheet) {
            HabitFilterView(selectedFilter: $selectedFilter)
        }
        .sheet(item: $selectedHabit) { habit in
            HabitDetailView(habit: habit)
        }
        .onAppear {
            Task {
                await habitService.fetchHabits()
            }
        }
    }
    
    // MARK: - Today's Progress Section
    
    private var todayProgressSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("今日进度")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(habitService.todayCompletedCount)/\(habitService.todayHabits.count)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // Progress Bar
            ProgressView(value: habitService.todayCompletionRate)
                .progressViewStyle(LinearProgressViewStyle(tint: .accentColor))
                .scaleEffect(x: 1, y: 2, anchor: .center)
            
            // Completion Rate
            Text("\(Int(habitService.todayCompletionRate * 100))% 完成")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .padding(.horizontal, 16)
        .padding(.top, 8)
    }
    
    // MARK: - Filter Chips
    
    private var filterChips: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(HabitFilter.allCases, id: \.self) { filter in
                    FilterChip(
                        title: filter.displayName,
                        isSelected: selectedFilter == filter,
                        count: getFilterCount(filter)
                    ) {
                        selectedFilter = filter
                        applyFilter(filter)
                    }
                }
            }
            .padding(.horizontal, 16)
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Habits List
    
    private var habitsList: some View {
        Group {
            if habitService.isLoading && habitService.habits.isEmpty {
                LoadingView()
            } else if habitService.habits.isEmpty {
                EmptyHabitsView()
            } else {
                List {
                    ForEach(filteredHabits) { habit in
                        HabitRowView(habit: habit) {
                            selectedHabit = habit
                        } onToggleRecord: {
                            Task {
                                try? await habitService.recordHabitCompletion(habitId: habit.id)
                            }
                        }
                        .listRowSeparator(.hidden)
                        .listRowInsets(EdgeInsets(top: 4, leading: 16, bottom: 4, trailing: 16))
                    }
                    .onDelete(perform: deleteHabits)
                }
                .listStyle(PlainListStyle())
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var filteredHabits: [Habit] {
        switch selectedFilter {
        case .all:
            return habitService.habits
        case .active:
            return habitService.activeHabits
        case .today:
            return habitService.todayHabits
        case .completed:
            return habitService.todayHabits.filter { habitService.isHabitCompletedToday($0.id) }
        case .pending:
            return habitService.todayHabits.filter { !habitService.isHabitCompletedToday($0.id) }
        }
    }
    
    // MARK: - Helper Methods
    
    private func getFilterCount(_ filter: HabitFilter) -> Int {
        switch filter {
        case .all:
            return habitService.habits.count
        case .active:
            return habitService.activeHabits.count
        case .today:
            return habitService.todayHabits.count
        case .completed:
            return habitService.todayHabits.filter { habitService.isHabitCompletedToday($0.id) }.count
        case .pending:
            return habitService.todayHabits.filter { !habitService.isHabitCompletedToday($0.id) }.count
        }
    }
    
    private func applyFilter(_ filter: HabitFilter) {
        Task {
            switch filter {
            case .all:
                await habitService.fetchHabits()
            case .active:
                await habitService.fetchHabits(isActive: true)
            case .today, .completed, .pending:
                await habitService.fetchHabits() // 通过计算属性过滤
            }
        }
    }
    
    private func deleteHabits(offsets: IndexSet) {
        for index in offsets {
            let habit = filteredHabits[index]
            Task {
                try? await habitService.deleteHabit(habit)
            }
        }
    }
}

// MARK: - Habit Filter

enum HabitFilter: CaseIterable {
    case all
    case active
    case today
    case completed
    case pending
    
    var displayName: String {
        switch self {
        case .all:
            return "全部"
        case .active:
            return "活跃"
        case .today:
            return "今日"
        case .completed:
            return "已完成"
        case .pending:
            return "待完成"
        }
    }
}

// MARK: - Empty Habits View

struct EmptyHabitsView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "target")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text("暂无习惯")
                .font(.title2)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Text("点击右上角的 + 按钮创建您的第一个习惯")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.horizontal, 32)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    HabitsView()
        .environmentObject(AppState())
}
