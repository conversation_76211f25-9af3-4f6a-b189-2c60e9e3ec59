//
//  AddHabitView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct AddHabitView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var habitService = HabitService.shared
    
    @State private var name = ""
    @State private var description = ""
    @State private var frequency: Habit.Frequency = .daily
    @State private var type: Habit.HabitType = .boolean
    @State private var targetValue = 1
    @State private var unit = ""
    @State private var selectedIcon = "target"
    @State private var selectedColor = "#4ECDC4"
    @State private var startDate = Date()
    @State private var hasStartDate = false
    @State private var endDate = Date()
    @State private var hasEndDate = false
    @State private var reminderTime = "09:00"
    @State private var isReminderEnabled = false
    
    @State private var isLoading = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    // Icon and Color options
    private let iconOptions = [
        "target", "figure.run", "book", "drop", "leaf", "heart",
        "moon.zzz", "dumbbell", "fork.knife", "brain.head.profile"
    ]
    
    private let colorOptions = [
        "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FECA57",
        "#FF9FF3", "#A29BFE", "#FD79A8", "#FDCB6E", "#6C5CE7"
    ]
    
    var body: some View {
        NavigationView {
            Form {
                // Basic Information
                basicInfoSection
                
                // Type and Target
                typeTargetSection
                
                // Frequency
                frequencySection
                
                // Appearance
                appearanceSection
                
                // Schedule
                scheduleSection
                
                // Reminder
                reminderSection
            }
            .navigationTitle("新建习惯")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveHabit()
                    }
                    .disabled(!isFormValid || isLoading)
                }
            }
        }
        .alert("错误", isPresented: $showingError) {
            Button("确定") {}
        } message: {
            Text(errorMessage)
        }
    }
    
    // MARK: - Basic Information Section
    
    private var basicInfoSection: some View {
        Section("基本信息") {
            TextField("习惯名称", text: $name)
                .textInputAutocapitalization(.sentences)
            
            TextField("习惯描述（可选）", text: $description, axis: .vertical)
                .lineLimit(2...4)
                .textInputAutocapitalization(.sentences)
        }
    }
    
    // MARK: - Type and Target Section
    
    private var typeTargetSection: some View {
        Section("类型和目标") {
            Picker("习惯类型", selection: $type) {
                ForEach(Habit.HabitType.allCases, id: \.self) { type in
                    HStack {
                        Image(systemName: type.iconName)
                        Text(type.displayName)
                    }
                    .tag(type)
                }
            }
            
            if type != .boolean {
                HStack {
                    Text("目标值")
                    Spacer()
                    TextField("目标值", value: $targetValue, format: .number)
                        .keyboardType(.numberPad)
                        .multilineTextAlignment(.trailing)
                        .frame(width: 80)
                }
                
                if type != .duration {
                    TextField("单位（可选）", text: $unit)
                        .textInputAutocapitalization(.never)
                }
            }
        }
    }
    
    // MARK: - Frequency Section
    
    private var frequencySection: some View {
        Section("频率") {
            Picker("执行频率", selection: $frequency) {
                ForEach(Habit.Frequency.allCases, id: \.self) { frequency in
                    HStack {
                        Image(systemName: frequency.iconName)
                        Text(frequency.displayName)
                    }
                    .tag(frequency)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    
    // MARK: - Appearance Section
    
    private var appearanceSection: some View {
        Section("外观") {
            // Icon Selection
            VStack(alignment: .leading, spacing: 8) {
                Text("图标")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 5), spacing: 12) {
                    ForEach(iconOptions, id: \.self) { icon in
                        Button(action: { selectedIcon = icon }) {
                            Image(systemName: icon)
                                .font(.title2)
                                .foregroundColor(selectedIcon == icon ? .white : Color.fromHexOrName(selectedColor))
                                .frame(width: 44, height: 44)
                                .background(
                                    Circle()
                                        .fill(selectedIcon == icon ? Color.fromHexOrName(selectedColor) : Color.fromHexOrName(selectedColor).opacity(0.1))
                                )
                        }
                    }
                }
            }
            
            // Color Selection
            VStack(alignment: .leading, spacing: 8) {
                Text("颜色")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 5), spacing: 12) {
                    ForEach(colorOptions, id: \.self) { color in
                        Button(action: { selectedColor = color }) {
                            Circle()
                                .fill(Color.fromHexOrName(color))
                                .frame(width: 32, height: 32)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white, lineWidth: selectedColor == color ? 3 : 0)
                                )
                                .shadow(color: .black.opacity(0.1), radius: 2)
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Schedule Section
    
    private var scheduleSection: some View {
        Section("时间安排") {
            Toggle("设置开始日期", isOn: $hasStartDate)
            
            if hasStartDate {
                DatePicker("开始日期", selection: $startDate, displayedComponents: .date)
                    .datePickerStyle(CompactDatePickerStyle())
            }
            
            Toggle("设置结束日期", isOn: $hasEndDate)
            
            if hasEndDate {
                DatePicker(
                    "结束日期",
                    selection: $endDate,
                    in: (hasStartDate ? startDate : Date())...,
                    displayedComponents: .date
                )
                .datePickerStyle(CompactDatePickerStyle())
            }
        }
    }
    
    // MARK: - Reminder Section
    
    private var reminderSection: some View {
        Section("提醒") {
            Toggle("启用提醒", isOn: $isReminderEnabled)
            
            if isReminderEnabled {
                HStack {
                    Text("提醒时间")
                    Spacer()
                    TextField("提醒时间", text: $reminderTime)
                        .multilineTextAlignment(.trailing)
                        .keyboardType(.numbersAndPunctuation)
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var isFormValid: Bool {
        !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        targetValue > 0
    }
    
    // MARK: - Actions
    
    private func saveHabit() {
        isLoading = true
        
        Task {
            do {
                try await habitService.createHabit(
                    name: name.trimmingCharacters(in: .whitespacesAndNewlines),
                    description: description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : description.trimmingCharacters(in: .whitespacesAndNewlines),
                    frequency: frequency,
                    type: type,
                    targetValue: targetValue,
                    unit: unit.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : unit.trimmingCharacters(in: .whitespacesAndNewlines),
                    icon: selectedIcon,
                    color: selectedColor,
                    startDate: hasStartDate ? startDate : nil,
                    endDate: hasEndDate ? endDate : nil,
                    reminderTime: isReminderEnabled ? reminderTime : nil,
                    isReminderEnabled: isReminderEnabled
                )
                
                await MainActor.run {
                    isLoading = false
                    dismiss()
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = error.localizedDescription
                    showingError = true
                }
            }
        }
    }
}

#Preview {
    AddHabitView()
}
