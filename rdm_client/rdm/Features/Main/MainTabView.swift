//
//  MainTabView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

struct MainTabView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // 任务页面
            TasksView()
                .tabItem {
                    Image(systemName: selectedTab == 0 ? "checkmark.circle.fill" : "checkmark.circle")
                    Text("任务")
                }
                .tag(0)
            
            // 习惯页面
            HabitsView()
                .tabItem {
                    Image(systemName: selectedTab == 1 ? "target" : "target")
                    Text("习惯")
                }
                .tag(1)
            
            // 统计页面
            StatisticsView()
                .tabItem {
                    Image(systemName: selectedTab == 2 ? "chart.bar.fill" : "chart.bar")
                    Text("统计")
                }
                .tag(2)
            
            // 设置页面
            SettingsView()
                .tabItem {
                    Image(systemName: selectedTab == 3 ? "gear" : "gear")
                    Text("设置")
                }
                .tag(3)
        }
        .accentColor(.primary)
        .onAppear {
            setupTabBarAppearance()
        }
    }
    
    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground
        
        // 设置选中状态的颜色
        appearance.selectionIndicatorTintColor = UIColor.systemBlue
        
        // 设置未选中状态的颜色
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.systemGray
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.systemGray
        ]
        
        // 设置选中状态的颜色
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor.systemBlue
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor.systemBlue
        ]
        
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

#Preview {
    MainTabView()
        .environmentObject(AppState())
}
