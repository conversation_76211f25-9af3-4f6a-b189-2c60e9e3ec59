//
//  StatisticsView.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI
import Charts

struct StatisticsView: View {
    @StateObject private var taskService = TaskService.shared
    @StateObject private var habitService = HabitService.shared
    @State private var selectedPeriod: StatisticsPeriod = .week
    @State private var taskStatistics: TaskStatistics?
    @State private var isLoading = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Period Selector
                    periodSelector
                    
                    // Overview Cards
                    overviewSection
                    
                    // Task Statistics
                    taskStatisticsSection
                    
                    // Habit Statistics
                    habitStatisticsSection
                    
                    // Productivity Chart
                    productivityChartSection
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
            }
            .navigationTitle("统计")
            .refreshable {
                await loadStatistics()
            }
        }
        .onAppear {
            Task {
                await loadStatistics()
            }
        }
    }
    
    // MARK: - Period Selector
    
    private var periodSelector: some View {
        Picker("时间段", selection: $selectedPeriod) {
            ForEach(StatisticsPeriod.allCases, id: \.self) { period in
                Text(period.displayName).tag(period)
            }
        }
        .pickerStyle(SegmentedPickerStyle())
        .onChange(of: selectedPeriod) { _ in
            Task {
                await loadStatistics()
            }
        }
    }
    
    // MARK: - Overview Section
    
    private var overviewSection: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
            OverviewCard(
                title: "任务完成率",
                value: "\(Int(taskService.completionRate * 100))%",
                icon: "checkmark.circle",
                color: .green,
                trend: .up
            )
            
            OverviewCard(
                title: "习惯完成率",
                value: "\(Int(habitService.todayCompletionRate * 100))%",
                icon: "target",
                color: .blue,
                trend: .stable
            )
            
            OverviewCard(
                title: "总任务数",
                value: "\(taskService.tasks.count)",
                icon: "list.bullet",
                color: .orange,
                trend: .up
            )
            
            OverviewCard(
                title: "活跃习惯",
                value: "\(habitService.activeHabits.count)",
                icon: "flame",
                color: .red,
                trend: .stable
            )
        }
    }
    
    // MARK: - Task Statistics Section
    
    private var taskStatisticsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("任务统计")
                .font(.headline)
                .fontWeight(.semibold)
            
            if let stats = taskStatistics {
                VStack(spacing: 12) {
                    // Task Status Distribution
                    TaskStatusChart(statistics: stats)
                    
                    // Priority Distribution
                    TaskPriorityChart(statistics: stats)
                }
            } else if isLoading {
                ProgressView()
                    .frame(height: 200)
            } else {
                Text("暂无数据")
                    .foregroundColor(.secondary)
                    .frame(height: 200)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Habit Statistics Section
    
    private var habitStatisticsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("习惯统计")
                .font(.headline)
                .fontWeight(.semibold)
            
            // Habit Completion Chart
            HabitCompletionChart(habits: habitService.activeHabits)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Productivity Chart Section
    
    private var productivityChartSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("生产力趋势")
                .font(.headline)
                .fontWeight(.semibold)
            
            ProductivityChart(period: selectedPeriod)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - Actions
    
    private func loadStatistics() async {
        isLoading = true
        
        do {
            let stats = try await taskService.getTaskStatistics(period: selectedPeriod.rawValue)
            await MainActor.run {
                taskStatistics = stats
                isLoading = false
            }
        } catch {
            await MainActor.run {
                isLoading = false
            }
        }
    }
}

// MARK: - Statistics Period

enum StatisticsPeriod: String, CaseIterable {
    case day = "day"
    case week = "week"
    case month = "month"
    
    var displayName: String {
        switch self {
        case .day:
            return "今日"
        case .week:
            return "本周"
        case .month:
            return "本月"
        }
    }
}

// MARK: - Overview Card

struct OverviewCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    let trend: Trend
    
    enum Trend {
        case up, down, stable
        
        var icon: String {
            switch self {
            case .up:
                return "arrow.up"
            case .down:
                return "arrow.down"
            case .stable:
                return "minus"
            }
        }
        
        var color: Color {
            switch self {
            case .up:
                return .green
            case .down:
                return .red
            case .stable:
                return .gray
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Spacer()
                
                Image(systemName: trend.icon)
                    .font(.caption)
                    .foregroundColor(trend.color)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Task Status Chart

struct TaskStatusChart: View {
    let statistics: TaskStatistics
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("任务状态分布")
                .font(.subheadline)
                .fontWeight(.medium)
            
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 8, height: 8)
                        Text("已完成")
                            .font(.caption)
                        Spacer()
                        Text("\(statistics.completed)")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    
                    HStack {
                        Circle()
                            .fill(Color.orange)
                            .frame(width: 8, height: 8)
                        Text("进行中")
                            .font(.caption)
                        Spacer()
                        Text("\(statistics.pending)")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    
                    if statistics.overdue > 0 {
                        HStack {
                            Circle()
                                .fill(Color.red)
                                .frame(width: 8, height: 8)
                            Text("已过期")
                                .font(.caption)
                            Spacer()
                            Text("\(statistics.overdue)")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                    }
                }
                
                Spacer()
                
                // Simple pie chart representation
                ZStack {
                    Circle()
                        .stroke(Color(.systemGray5), lineWidth: 8)
                        .frame(width: 60, height: 60)
                    
                    Circle()
                        .trim(from: 0, to: CGFloat(statistics.completionRateValue / 100))
                        .stroke(Color.green, lineWidth: 8)
                        .rotationEffect(.degrees(-90))
                        .frame(width: 60, height: 60)
                    
                    Text("\(Int(statistics.completionRateValue))%")
                        .font(.caption)
                        .fontWeight(.bold)
                }
            }
        }
    }
}

// MARK: - Task Priority Chart

struct TaskPriorityChart: View {
    let statistics: TaskStatistics
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("优先级分布")
                .font(.subheadline)
                .fontWeight(.medium)
            
            VStack(spacing: 8) {
                PriorityBar(
                    title: "高优先级",
                    count: statistics.byPriority.high,
                    total: statistics.total,
                    color: .red
                )
                
                PriorityBar(
                    title: "中优先级",
                    count: statistics.byPriority.medium,
                    total: statistics.total,
                    color: .orange
                )
                
                PriorityBar(
                    title: "低优先级",
                    count: statistics.byPriority.low,
                    total: statistics.total,
                    color: .gray
                )
            }
        }
    }
}

// MARK: - Priority Bar

struct PriorityBar: View {
    let title: String
    let count: Int
    let total: Int
    let color: Color
    
    private var percentage: Double {
        total > 0 ? Double(count) / Double(total) : 0
    }
    
    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
                .frame(width: 60, alignment: .leading)
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 8)
                        .cornerRadius(4)
                    
                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * percentage, height: 8)
                        .cornerRadius(4)
                }
            }
            .frame(height: 8)
            
            Text("\(count)")
                .font(.caption)
                .fontWeight(.medium)
                .frame(width: 30, alignment: .trailing)
        }
    }
}

// MARK: - Habit Completion Chart

struct HabitCompletionChart: View {
    let habits: [Habit]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("习惯完成情况")
                .font(.subheadline)
                .fontWeight(.medium)
            
            if habits.isEmpty {
                Text("暂无活跃习惯")
                    .foregroundColor(.secondary)
                    .frame(height: 100)
            } else {
                VStack(spacing: 8) {
                    ForEach(habits.prefix(5), id: \.id) { habit in
                        HabitProgressRow(habit: habit)
                    }
                }
            }
        }
    }
}

// MARK: - Habit Progress Row

struct HabitProgressRow: View {
    let habit: Habit
    
    var body: some View {
        HStack {
            HStack(spacing: 8) {
                Image(systemName: habit.icon ?? "target")
                    .font(.caption)
                    .foregroundColor(Color.fromHexOrName(habit.color ?? "#4ECDC4"))
                    .frame(width: 16)
                
                Text(habit.name)
                    .font(.caption)
                    .lineLimit(1)
            }
            .frame(width: 100, alignment: .leading)
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 6)
                        .cornerRadius(3)
                    
                    Rectangle()
                        .fill(Color.fromHexOrName(habit.color ?? "#4ECDC4"))
                        .frame(width: geometry.size.width * streakProgress, height: 6)
                        .cornerRadius(3)
                }
            }
            .frame(height: 6)
            
            Text("\(habit.currentStreak)天")
                .font(.caption)
                .fontWeight(.medium)
                .frame(width: 40, alignment: .trailing)
        }
    }
    
    private var streakProgress: Double {
        let maxStreak = max(habit.bestStreak, 1)
        return Double(habit.currentStreak) / Double(maxStreak)
    }
}

// MARK: - Productivity Chart

struct ProductivityChart: View {
    let period: StatisticsPeriod
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("生产力指数")
                .font(.subheadline)
                .fontWeight(.medium)
            
            // Placeholder chart
            HStack(alignment: .bottom, spacing: 8) {
                ForEach(0..<7, id: \.self) { index in
                    VStack(spacing: 4) {
                        Rectangle()
                            .fill(Color.accentColor.opacity(0.7))
                            .frame(width: 24, height: CGFloat.random(in: 20...80))
                            .cornerRadius(4)
                        
                        Text(dayLabel(for: index))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .frame(height: 100)
        }
    }
    
    private func dayLabel(for index: Int) -> String {
        let calendar = Calendar.current
        let date = calendar.date(byAdding: .day, value: index - 6, to: Date()) ?? Date()
        let formatter = DateFormatter()
        formatter.dateFormat = "E"
        return formatter.string(from: date)
    }
}

#Preview {
    StatisticsView()
        .environmentObject(AppState())
}
