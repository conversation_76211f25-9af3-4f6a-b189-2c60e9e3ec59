//
//  CacheManager.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation

/// 缓存管理器
class CacheManager {
    static let shared = CacheManager()
    
    // MARK: - Private Properties
    
    private let fileManager = FileManager.default
    private let cacheDirectory: URL
    private let memoryCache = NSCache<NSString, NSData>()
    private let queue = DispatchQueue(label: "CacheManager", qos: .utility)
    
    // MARK: - Configuration
    
    private struct Configuration {
        static let maxMemoryCacheSize = 50 * 1024 * 1024 // 50MB
        static let maxDiskCacheSize = 200 * 1024 * 1024 // 200MB
        static let defaultExpirationTime: TimeInterval = 7 * 24 * 60 * 60 // 7天
        static let cacheDirectoryName = "TaskFlowCache"
    }
    
    // MARK: - Initialization
    
    private init() {
        // 设置缓存目录
        let cachesDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
        cacheDirectory = cachesDirectory.appendingPathComponent(Configuration.cacheDirectoryName)
        
        // 创建缓存目录
        createCacheDirectoryIfNeeded()
        
        // 配置内存缓存
        setupMemoryCache()
        
        // 清理过期缓存
        cleanupExpiredCache()
    }
    
    // MARK: - Public Methods
    
    /// 保存数据到缓存
    func setData(_ data: Data, forKey key: String, expirationTime: TimeInterval = Configuration.defaultExpirationTime) {
        queue.async { [weak self] in
            self?.setDataSync(data, forKey: key, expirationTime: expirationTime)
        }
    }
    
    /// 从缓存获取数据
    func getData(forKey key: String) -> Data? {
        // 先检查内存缓存
        if let data = memoryCache.object(forKey: key as NSString) {
            return data as Data
        }
        
        // 检查磁盘缓存
        return queue.sync { [weak self] in
            return self?.getDataSync(forKey: key)
        }
    }
    
    /// 异步获取数据
    func getData(forKey key: String, completion: @escaping (Data?) -> Void) {
        // 先检查内存缓存
        if let data = memoryCache.object(forKey: key as NSString) {
            completion(data as Data)
            return
        }
        
        // 异步检查磁盘缓存
        queue.async { [weak self] in
            let data = self?.getDataSync(forKey: key)
            DispatchQueue.main.async {
                completion(data)
            }
        }
    }
    
    /// 删除指定键的缓存
    func removeData(forKey key: String) {
        // 从内存缓存移除
        memoryCache.removeObject(forKey: key as NSString)
        
        // 从磁盘缓存移除
        queue.async { [weak self] in
            self?.removeDataSync(forKey: key)
        }
    }
    
    /// 清除所有缓存
    func clearAllCache() {
        // 清除内存缓存
        memoryCache.removeAllObjects()
        
        // 清除磁盘缓存
        queue.async { [weak self] in
            self?.clearAllCacheSync()
        }
    }
    
    /// 获取缓存大小
    func getCacheSize() -> Int64 {
        return queue.sync { [weak self] in
            return self?.getCacheSizeSync() ?? 0
        }
    }
    
    /// 异步获取缓存大小
    func getCacheSize(completion: @escaping (Int64) -> Void) {
        queue.async { [weak self] in
            let size = self?.getCacheSizeSync() ?? 0
            DispatchQueue.main.async {
                completion(size)
            }
        }
    }
    
    /// 检查缓存是否存在
    func containsData(forKey key: String) -> Bool {
        // 检查内存缓存
        if memoryCache.object(forKey: key as NSString) != nil {
            return true
        }
        
        // 检查磁盘缓存
        return queue.sync { [weak self] in
            return self?.containsDataSync(forKey: key) ?? false
        }
    }
    
    // MARK: - Private Methods
    
    private func createCacheDirectoryIfNeeded() {
        if !fileManager.fileExists(atPath: cacheDirectory.path) {
            try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        }
    }
    
    private func setupMemoryCache() {
        memoryCache.totalCostLimit = Configuration.maxMemoryCacheSize
        memoryCache.countLimit = 100 // 最多缓存100个对象
        
        // 监听内存警告
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.memoryCache.removeAllObjects()
        }
    }
    
    private func setDataSync(_ data: Data, forKey key: String, expirationTime: TimeInterval) {
        // 保存到内存缓存
        memoryCache.setObject(data as NSData, forKey: key as NSString, cost: data.count)
        
        // 保存到磁盘缓存
        let fileURL = cacheFileURL(forKey: key)
        let metadataURL = cacheMetadataURL(forKey: key)
        
        do {
            // 保存数据
            try data.write(to: fileURL)
            
            // 保存元数据
            let metadata = CacheMetadata(
                key: key,
                createdAt: Date(),
                expiresAt: Date().addingTimeInterval(expirationTime),
                size: data.count
            )
            
            let metadataData = try JSONEncoder().encode(metadata)
            try metadataData.write(to: metadataURL)
            
        } catch {
            print("Failed to save cache for key \(key): \(error)")
        }
    }
    
    private func getDataSync(forKey key: String) -> Data? {
        let fileURL = cacheFileURL(forKey: key)
        let metadataURL = cacheMetadataURL(forKey: key)
        
        // 检查文件是否存在
        guard fileManager.fileExists(atPath: fileURL.path),
              fileManager.fileExists(atPath: metadataURL.path) else {
            return nil
        }
        
        do {
            // 读取元数据
            let metadataData = try Data(contentsOf: metadataURL)
            let metadata = try JSONDecoder().decode(CacheMetadata.self, from: metadataData)
            
            // 检查是否过期
            if Date() > metadata.expiresAt {
                removeDataSync(forKey: key)
                return nil
            }
            
            // 读取数据
            let data = try Data(contentsOf: fileURL)
            
            // 添加到内存缓存
            memoryCache.setObject(data as NSData, forKey: key as NSString, cost: data.count)
            
            return data
            
        } catch {
            print("Failed to read cache for key \(key): \(error)")
            removeDataSync(forKey: key)
            return nil
        }
    }
    
    private func removeDataSync(forKey key: String) {
        let fileURL = cacheFileURL(forKey: key)
        let metadataURL = cacheMetadataURL(forKey: key)
        
        try? fileManager.removeItem(at: fileURL)
        try? fileManager.removeItem(at: metadataURL)
    }
    
    private func clearAllCacheSync() {
        try? fileManager.removeItem(at: cacheDirectory)
        createCacheDirectoryIfNeeded()
    }
    
    private func getCacheSizeSync() -> Int64 {
        guard let enumerator = fileManager.enumerator(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey]) else {
            return 0
        }
        
        var totalSize: Int64 = 0
        
        for case let fileURL as URL in enumerator {
            do {
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                if let fileSize = resourceValues.fileSize {
                    totalSize += Int64(fileSize)
                }
            } catch {
                continue
            }
        }
        
        return totalSize
    }
    
    private func containsDataSync(forKey key: String) -> Bool {
        let fileURL = cacheFileURL(forKey: key)
        let metadataURL = cacheMetadataURL(forKey: key)
        
        guard fileManager.fileExists(atPath: fileURL.path),
              fileManager.fileExists(atPath: metadataURL.path) else {
            return false
        }
        
        // 检查是否过期
        do {
            let metadataData = try Data(contentsOf: metadataURL)
            let metadata = try JSONDecoder().decode(CacheMetadata.self, from: metadataData)
            
            if Date() > metadata.expiresAt {
                removeDataSync(forKey: key)
                return false
            }
            
            return true
        } catch {
            removeDataSync(forKey: key)
            return false
        }
    }
    
    private func cleanupExpiredCache() {
        queue.async { [weak self] in
            self?.cleanupExpiredCacheSync()
        }
    }
    
    private func cleanupExpiredCacheSync() {
        guard let enumerator = fileManager.enumerator(at: cacheDirectory, includingPropertiesForKeys: nil) else {
            return
        }
        
        for case let fileURL as URL in enumerator {
            if fileURL.pathExtension == "metadata" {
                do {
                    let metadataData = try Data(contentsOf: fileURL)
                    let metadata = try JSONDecoder().decode(CacheMetadata.self, from: metadataData)
                    
                    if Date() > metadata.expiresAt {
                        removeDataSync(forKey: metadata.key)
                    }
                } catch {
                    try? fileManager.removeItem(at: fileURL)
                }
            }
        }
        
        // 检查缓存大小，如果超过限制则清理最旧的文件
        let currentSize = getCacheSizeSync()
        if currentSize > Configuration.maxDiskCacheSize {
            cleanupOldestCache()
        }
    }
    
    private func cleanupOldestCache() {
        guard let enumerator = fileManager.enumerator(at: cacheDirectory, includingPropertiesForKeys: [.creationDateKey]) else {
            return
        }
        
        var files: [(url: URL, creationDate: Date)] = []
        
        for case let fileURL as URL in enumerator {
            if fileURL.pathExtension == "metadata" {
                do {
                    let resourceValues = try fileURL.resourceValues(forKeys: [.creationDateKey])
                    if let creationDate = resourceValues.creationDate {
                        files.append((url: fileURL, creationDate: creationDate))
                    }
                } catch {
                    continue
                }
            }
        }
        
        // 按创建时间排序，删除最旧的文件
        files.sort { $0.creationDate < $1.creationDate }
        
        let filesToDelete = files.prefix(files.count / 4) // 删除25%的最旧文件
        
        for file in filesToDelete {
            do {
                let metadataData = try Data(contentsOf: file.url)
                let metadata = try JSONDecoder().decode(CacheMetadata.self, from: metadataData)
                removeDataSync(forKey: metadata.key)
            } catch {
                try? fileManager.removeItem(at: file.url)
            }
        }
    }
    
    private func cacheFileURL(forKey key: String) -> URL {
        let fileName = key.addingPercentEncoding(withAllowedCharacters: .alphanumerics) ?? key
        return cacheDirectory.appendingPathComponent("\(fileName).cache")
    }
    
    private func cacheMetadataURL(forKey key: String) -> URL {
        let fileName = key.addingPercentEncoding(withAllowedCharacters: .alphanumerics) ?? key
        return cacheDirectory.appendingPathComponent("\(fileName).metadata")
    }
}

// MARK: - Cache Metadata

private struct CacheMetadata: Codable {
    let key: String
    let createdAt: Date
    let expiresAt: Date
    let size: Int
}

// MARK: - Convenience Methods

extension CacheManager {
    /// 缓存 Codable 对象
    func setObject<T: Codable>(_ object: T, forKey key: String, expirationTime: TimeInterval = Configuration.defaultExpirationTime) {
        do {
            let data = try JSONEncoder().encode(object)
            setData(data, forKey: key, expirationTime: expirationTime)
        } catch {
            print("Failed to encode object for key \(key): \(error)")
        }
    }
    
    /// 获取 Codable 对象
    func getObject<T: Codable>(_ type: T.Type, forKey key: String) -> T? {
        guard let data = getData(forKey: key) else { return nil }
        
        do {
            return try JSONDecoder().decode(type, from: data)
        } catch {
            print("Failed to decode object for key \(key): \(error)")
            removeData(forKey: key) // 移除无效缓存
            return nil
        }
    }
    
    /// 异步获取 Codable 对象
    func getObject<T: Codable>(_ type: T.Type, forKey key: String, completion: @escaping (T?) -> Void) {
        getData(forKey: key) { data in
            guard let data = data else {
                completion(nil)
                return
            }
            
            do {
                let object = try JSONDecoder().decode(type, from: data)
                completion(object)
            } catch {
                print("Failed to decode object for key \(key): \(error)")
                self.removeData(forKey: key) // 移除无效缓存
                completion(nil)
            }
        }
    }
}
