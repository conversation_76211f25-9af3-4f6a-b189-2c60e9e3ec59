//
//  ColorExtensions.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI

// MARK: - Color Extensions

extension Color {
    /// Create a Color from a color name string
    static func fromColorName(_ colorName: String) -> Color {
        switch colorName.lowercased() {
        case "red":
            return .red
        case "orange":
            return .orange
        case "yellow":
            return .yellow
        case "green":
            return .green
        case "blue":
            return .blue
        case "purple":
            return .purple
        case "gray":
            return .gray
        default:
            return .accentColor
        }
    }
    
    /// Create a Color from a hex string or color name
    static func fromHexOrName(_ colorString: String) -> Color {
        switch colorString.lowercased() {
        case "#ff6b6b", "red":
            return .red
        case "#4ecdc4", "teal":
            return .teal
        case "#45b7d1", "blue":
            return .blue
        case "#96ceb4", "green":
            return .green
        case "#feca57", "yellow":
            return .yellow
        case "#ff9ff3", "pink":
            return .pink
        case "#a29bfe", "purple":
            return .purple
        case "#fd79a8", "magenta":
            return Color(red: 0.99, green: 0.47, blue: 0.66)
        case "#fdcb6e", "orange":
            return .orange
        case "#6c5ce7", "indigo":
            return .indigo
        default:
            return .accentColor
        }
    }
    
    /// Create a Color from a hex string
    static func fromHex(_ hex: String) -> Color {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        return Color(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}
