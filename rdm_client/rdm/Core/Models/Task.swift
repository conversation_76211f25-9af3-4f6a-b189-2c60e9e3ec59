//
//  Task.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation

/// 任务模型
struct Task: Codable, Identifiable, Equatable {
    let id: String
    var title: String
    var description: String?
    var isCompleted: Bool
    var priority: Priority
    var status: Status
    var dueDate: Date?
    var category: String?
    var tags: [String]
    var estimatedDuration: TimeInterval?
    var actualDuration: TimeInterval?
    var completedAt: Date?
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id = "objectId"
        case title
        case description
        case isCompleted
        case priority
        case status
        case dueDate
        case category
        case tags
        case estimatedDuration
        case actualDuration
        case completedAt
        case createdAt
        case updatedAt
    }
}

// MARK: - Task Enums

extension Task {
    /// 任务优先级
    enum Priority: String, Codable, CaseIterable {
        case high = "high"
        case medium = "medium"
        case low = "low"
        
        var displayName: String {
            switch self {
            case .high:
                return "高"
            case .medium:
                return "中"
            case .low:
                return "低"
            }
        }
        
        var color: String {
            switch self {
            case .high:
                return "red"
            case .medium:
                return "orange"
            case .low:
                return "gray"
            }
        }
        
        var iconName: String {
            switch self {
            case .high:
                return "exclamationmark.3"
            case .medium:
                return "exclamationmark.2"
            case .low:
                return "minus"
            }
        }
        
        var sortOrder: Int {
            switch self {
            case .high:
                return 3
            case .medium:
                return 2
            case .low:
                return 1
            }
        }
    }
    
    /// 任务状态
    enum Status: String, Codable, CaseIterable {
        case pending = "pending"
        case inProgress = "in_progress"
        case completed = "completed"
        case cancelled = "cancelled"
        
        var displayName: String {
            switch self {
            case .pending:
                return "待处理"
            case .inProgress:
                return "进行中"
            case .completed:
                return "已完成"
            case .cancelled:
                return "已取消"
            }
        }
        
        var iconName: String {
            switch self {
            case .pending:
                return "circle"
            case .inProgress:
                return "clock"
            case .completed:
                return "checkmark.circle.fill"
            case .cancelled:
                return "xmark.circle"
            }
        }
        
        var color: String {
            switch self {
            case .pending:
                return "gray"
            case .inProgress:
                return "blue"
            case .completed:
                return "green"
            case .cancelled:
                return "red"
            }
        }
    }
}

// MARK: - Task Extensions

extension Task {
    /// 是否过期
    var isOverdue: Bool {
        guard let dueDate = dueDate, !isCompleted else { return false }
        return Date() > dueDate
    }
    
    /// 剩余时间
    var timeRemaining: TimeInterval? {
        guard let dueDate = dueDate, !isCompleted else { return nil }
        let remaining = dueDate.timeIntervalSinceNow
        return remaining > 0 ? remaining : nil
    }
    
    /// 剩余时间描述
    var timeRemainingDescription: String? {
        guard let timeRemaining = timeRemaining else { return nil }
        
        let days = Int(timeRemaining / (24 * 60 * 60))
        let hours = Int((timeRemaining.truncatingRemainder(dividingBy: 24 * 60 * 60)) / (60 * 60))
        
        if days > 0 {
            return "\(days)天"
        } else if hours > 0 {
            return "\(hours)小时"
        } else {
            return "不到1小时"
        }
    }
    
    /// 过期时间描述
    var overdueDescription: String? {
        guard isOverdue, let dueDate = dueDate else { return nil }
        
        let overdue = Date().timeIntervalSince(dueDate)
        let days = Int(overdue / (24 * 60 * 60))
        let hours = Int((overdue.truncatingRemainder(dividingBy: 24 * 60 * 60)) / (60 * 60))
        
        if days > 0 {
            return "过期\(days)天"
        } else if hours > 0 {
            return "过期\(hours)小时"
        } else {
            return "刚过期"
        }
    }
    
    /// 完成用时描述
    var completionDurationDescription: String? {
        guard let actualDuration = actualDuration else { return nil }
        
        let hours = Int(actualDuration / 3600)
        let minutes = Int((actualDuration.truncatingRemainder(dividingBy: 3600)) / 60)
        
        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
    
    /// 预估时间描述
    var estimatedDurationDescription: String? {
        guard let estimatedDuration = estimatedDuration else { return nil }
        
        let hours = Int(estimatedDuration / 3600)
        let minutes = Int((estimatedDuration.truncatingRemainder(dividingBy: 3600)) / 60)
        
        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
    
    /// 标签显示文本
    var tagsDisplayText: String {
        tags.isEmpty ? "" : tags.joined(separator: ", ")
    }
}

// MARK: - Task Creation

extension Task {
    /// 创建新任务
    static func create(
        title: String,
        description: String? = nil,
        priority: Priority = .medium,
        dueDate: Date? = nil,
        category: String? = nil,
        tags: [String] = [],
        estimatedDuration: TimeInterval? = nil
    ) -> Task {
        Task(
            id: UUID().uuidString,
            title: title,
            description: description,
            isCompleted: false,
            priority: priority,
            status: .pending,
            dueDate: dueDate,
            category: category,
            tags: tags,
            estimatedDuration: estimatedDuration,
            actualDuration: nil,
            completedAt: nil,
            createdAt: Date(),
            updatedAt: Date()
        )
    }
    
    /// 预览任务（用于SwiftUI预览）
    static func preview() -> Task {
        Task(
            id: "preview-task-id",
            title: "完成项目文档",
            description: "编写技术文档和用户手册，包括API文档和部署指南",
            isCompleted: false,
            priority: .high,
            status: .inProgress,
            dueDate: Calendar.current.date(byAdding: .day, value: 2, to: Date()),
            category: "工作",
            tags: ["文档", "项目", "重要"],
            estimatedDuration: 4 * 3600, // 4小时
            actualDuration: nil,
            completedAt: nil,
            createdAt: Date().addingTimeInterval(-2 * 24 * 60 * 60), // 2天前
            updatedAt: Date()
        )
    }
    
    /// 已完成任务预览
    static func completedPreview() -> Task {
        var task = preview()
        task.isCompleted = true
        task.status = .completed
        task.completedAt = Date().addingTimeInterval(-60 * 60) // 1小时前
        task.actualDuration = 3.5 * 3600 // 3.5小时
        return task
    }
}

// MARK: - Task Update Request

/// 任务更新请求
struct TaskUpdateRequest: Codable {
    let title: String?
    let description: String?
    let isCompleted: Bool?
    let priority: Task.Priority?
    let status: Task.Status?
    let dueDate: Date?
    let category: String?
    let tags: [String]?
    let estimatedDuration: TimeInterval?
    let actualDuration: TimeInterval?
    
    init(
        title: String? = nil,
        description: String? = nil,
        isCompleted: Bool? = nil,
        priority: Task.Priority? = nil,
        status: Task.Status? = nil,
        dueDate: Date? = nil,
        category: String? = nil,
        tags: [String]? = nil,
        estimatedDuration: TimeInterval? = nil,
        actualDuration: TimeInterval? = nil
    ) {
        self.title = title
        self.description = description
        self.isCompleted = isCompleted
        self.priority = priority
        self.status = status
        self.dueDate = dueDate
        self.category = category
        self.tags = tags
        self.estimatedDuration = estimatedDuration
        self.actualDuration = actualDuration
    }
}

/// 批量任务操作请求
struct BatchTaskRequest: Codable {
    let taskIds: [String]
    let operation: Operation
    let data: TaskUpdateRequest?
    
    enum Operation: String, Codable {
        case complete = "complete"
        case incomplete = "incomplete"
        case delete = "delete"
        case update = "update"
    }
}

/// 任务统计信息
struct TaskStatistics: Codable {
    let total: Int
    let completed: Int
    let pending: Int
    let overdue: Int
    let completionRate: String
    let periodCompleted: Int
    let byPriority: PriorityStatistics
    
    struct PriorityStatistics: Codable {
        let high: Int
        let medium: Int
        let low: Int
    }
    
    var completionRateValue: Double {
        Double(completionRate) ?? 0.0
    }
}
