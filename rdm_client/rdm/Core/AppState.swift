//
//  AppState.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import SwiftUI
import Combine

/// 应用全局状态管理
@MainActor
class AppState: ObservableObject {
    // MARK: - Published Properties
    
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var networkStatus: NetworkStatus = .unknown
    @Published var appTheme: AppTheme = .system
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
    private let authService = AuthService.shared
    private let networkMonitor = NetworkMonitor.shared
    
    // MARK: - Initialization
    
    init() {
        setupBindings()
        loadUserPreferences()
    }
    
    // MARK: - Public Methods
    
    func initialize() {
        checkAuthenticationStatus()
        startNetworkMonitoring()
    }
    
    func signIn(email: String, password: String) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let user = try await authService.signIn(email: email, password: password)
            currentUser = user
            isAuthenticated = true
            saveUserPreferences()
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    func signInWithApple() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let user = try await authService.signInWithApple()
            currentUser = user
            isAuthenticated = true
            saveUserPreferences()
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    func signUp(email: String, password: String, username: String) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let user = try await authService.signUp(email: email, password: password, username: username)
            currentUser = user
            isAuthenticated = true
            saveUserPreferences()
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    func signOut() async {
        isLoading = true
        
        do {
            try await authService.signOut()
            currentUser = nil
            isAuthenticated = false
            clearUserPreferences()
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    func updateTheme(_ theme: AppTheme) {
        appTheme = theme
        saveUserPreferences()
    }
    
    func clearError() {
        errorMessage = nil
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // 监听认证状态变化
        authService.$isAuthenticated
            .receive(on: DispatchQueue.main)
            .assign(to: \.isAuthenticated, on: self)
            .store(in: &cancellables)
        
        authService.$currentUser
            .receive(on: DispatchQueue.main)
            .assign(to: \.currentUser, on: self)
            .store(in: &cancellables)
        
        // 监听网络状态变化
        networkMonitor.$status
            .receive(on: DispatchQueue.main)
            .sink { [weak self] status in
                self?.networkStatus = status
            }
            .store(in: &cancellables)
    }
    
    private func checkAuthenticationStatus() {
        Task {
            await authService.checkAuthenticationStatus()
        }
    }
    
    private func startNetworkMonitoring() {
        networkMonitor.startMonitoring()
    }
    
    private func loadUserPreferences() {
        // 加载主题设置
        if let themeRawValue = UserDefaults.standard.object(forKey: "app_theme") as? String,
           let theme = AppTheme(rawValue: themeRawValue) {
            appTheme = theme
        }
    }
    
    private func saveUserPreferences() {
        UserDefaults.standard.set(appTheme.rawValue, forKey: "app_theme")
    }
    
    private func clearUserPreferences() {
        UserDefaults.standard.removeObject(forKey: "app_theme")
    }
}

// MARK: - Supporting Types

enum AppTheme: String, CaseIterable {
    case light = "light"
    case dark = "dark"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .light:
            return "浅色"
        case .dark:
            return "深色"
        case .system:
            return "跟随系统"
        }
    }
    
    var colorScheme: ColorScheme? {
        switch self {
        case .light:
            return .light
        case .dark:
            return .dark
        case .system:
            return nil
        }
    }
}

// MARK: - AppState Extensions

extension AppState {
    var isNetworkAvailable: Bool {
        networkStatus.isConnected
    }
    
    var shouldShowOfflineIndicator: Bool {
        !isNetworkAvailable
    }
    
    var canPerformNetworkOperations: Bool {
        isNetworkAvailable && isAuthenticated
    }
}
