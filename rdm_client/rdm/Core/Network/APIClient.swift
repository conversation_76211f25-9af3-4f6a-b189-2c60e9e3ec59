//
//  APIClient.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation
import Combine

/// API 客户端
class APIClient: ObservableObject {
    static let shared = APIClient()
    
    // MARK: - Properties
    
    private let baseURL: URL
    private let session: URLSession
    private var authToken: String?
    
    // MARK: - Configuration
    
    private struct Configuration {
        static let baseURL = "https://api.taskflow.app/api/v1"
        static let timeout: TimeInterval = 30
        static let maxRetries = 3
    }
    
    // MARK: - Initialization
    
    private init() {
        guard let url = URL(string: Configuration.baseURL) else {
            fatalError("Invalid base URL")
        }
        
        self.baseURL = url
        
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = Configuration.timeout
        config.timeoutIntervalForResource = Configuration.timeout * 2
        config.requestCachePolicy = .reloadIgnoringLocalCacheData
        
        self.session = URLSession(configuration: config)
    }
    
    // MARK: - Authentication
    
    func setAuthToken(_ token: String) {
        authToken = token
    }
    
    func clearAuthToken() {
        authToken = nil
    }
    
    // MARK: - Auth API
    
    func signUp(_ request: SignUpRequest) async throws -> AuthResponse {
        return try await performRequest(
            endpoint: .signUp,
            method: .POST,
            body: request
        )
    }
    
    func signIn(_ request: SignInRequest) async throws -> AuthResponse {
        return try await performRequest(
            endpoint: .signIn,
            method: .POST,
            body: request
        )
    }
    
    func signInWithApple(_ request: AppleSignInRequest) async throws -> AuthResponse {
        return try await performRequest(
            endpoint: .signInWithApple,
            method: .POST,
            body: request
        )
    }
    
    func signOut() async throws {
        try await performRequest(
            endpoint: .signOut,
            method: .POST,
            requiresAuth: true
        )
    }
    
    func forgotPassword(_ request: ForgotPasswordRequest) async throws {
        try await performRequest(
            endpoint: .forgotPassword,
            method: .POST,
            body: request
        )
    }
    
    // MARK: - User API
    
    func getCurrentUser() async throws -> User {
        let response: APIResponse<User> = try await performRequest(
            endpoint: .getCurrentUser,
            method: .GET,
            requiresAuth: true
        )
        return response.data
    }
    
    func updateUser(_ request: UserUpdateRequest) async throws -> User {
        let response: APIResponse<User> = try await performRequest(
            endpoint: .updateUser,
            method: .PUT,
            body: request,
            requiresAuth: true
        )
        return response.data
    }
    
    func changePassword(_ request: ChangePasswordRequest) async throws {
        try await performRequest(
            endpoint: .changePassword,
            method: .POST,
            body: request,
            requiresAuth: true
        )
    }
    
    func getUserStatistics() async throws -> UserStatistics {
        let response: APIResponse<UserStatistics> = try await performRequest(
            endpoint: .getUserStatistics,
            method: .GET,
            requiresAuth: true
        )
        return response.data
    }
    
    // MARK: - Task API
    
    func getTasks(
        page: Int = 1,
        limit: Int = 20,
        status: String? = nil,
        priority: String? = nil,
        isCompleted: Bool? = nil,
        category: String? = nil,
        search: String? = nil,
        sortBy: String = "createdAt",
        sortOrder: String = "desc"
    ) async throws -> TaskListResponse {
        var queryItems: [URLQueryItem] = [
            URLQueryItem(name: "page", value: "\(page)"),
            URLQueryItem(name: "limit", value: "\(limit)"),
            URLQueryItem(name: "sortBy", value: sortBy),
            URLQueryItem(name: "sortOrder", value: sortOrder)
        ]
        
        if let status = status {
            queryItems.append(URLQueryItem(name: "status", value: status))
        }
        
        if let priority = priority {
            queryItems.append(URLQueryItem(name: "priority", value: priority))
        }
        
        if let isCompleted = isCompleted {
            queryItems.append(URLQueryItem(name: "isCompleted", value: "\(isCompleted)"))
        }
        
        if let category = category {
            queryItems.append(URLQueryItem(name: "category", value: category))
        }
        
        if let search = search {
            queryItems.append(URLQueryItem(name: "search", value: search))
        }
        
        let response: APIResponse<TaskListResponse> = try await performRequest(
            endpoint: .getTasks,
            method: .GET,
            queryItems: queryItems,
            requiresAuth: true
        )
        return response.data
    }
    
    func createTask(_ request: TaskCreateRequest) async throws -> Task {
        let response: APIResponse<Task> = try await performRequest(
            endpoint: .createTask,
            method: .POST,
            body: request,
            requiresAuth: true
        )
        return response.data
    }
    
    func getTask(_ taskId: String) async throws -> Task {
        let response: APIResponse<Task> = try await performRequest(
            endpoint: .getTask(taskId),
            method: .GET,
            requiresAuth: true
        )
        return response.data
    }
    
    func updateTask(_ taskId: String, _ request: TaskUpdateRequest) async throws -> Task {
        let response: APIResponse<Task> = try await performRequest(
            endpoint: .updateTask(taskId),
            method: .PUT,
            body: request,
            requiresAuth: true
        )
        return response.data
    }
    
    func deleteTask(_ taskId: String) async throws {
        try await performRequest(
            endpoint: .deleteTask(taskId),
            method: .DELETE,
            requiresAuth: true
        )
    }
    
    func batchUpdateTasks(_ request: BatchTaskRequest) async throws {
        try await performRequest(
            endpoint: .batchUpdateTasks,
            method: .POST,
            body: request,
            requiresAuth: true
        )
    }
    
    func getTaskStatistics(period: String = "week") async throws -> TaskStatistics {
        let queryItems = [URLQueryItem(name: "period", value: period)]
        
        let response: APIResponse<TaskStatistics> = try await performRequest(
            endpoint: .getTaskStatistics,
            method: .GET,
            queryItems: queryItems,
            requiresAuth: true
        )
        return response.data
    }
    
    // MARK: - Habit API
    
    func getHabits(
        page: Int = 1,
        limit: Int = 20,
        frequency: String? = nil,
        isActive: Bool? = nil
    ) async throws -> HabitListResponse {
        var queryItems: [URLQueryItem] = [
            URLQueryItem(name: "page", value: "\(page)"),
            URLQueryItem(name: "limit", value: "\(limit)")
        ]
        
        if let frequency = frequency {
            queryItems.append(URLQueryItem(name: "frequency", value: frequency))
        }
        
        if let isActive = isActive {
            queryItems.append(URLQueryItem(name: "isActive", value: "\(isActive)"))
        }
        
        let response: APIResponse<HabitListResponse> = try await performRequest(
            endpoint: .getHabits,
            method: .GET,
            queryItems: queryItems,
            requiresAuth: true
        )
        return response.data
    }
    
    func createHabit(_ request: HabitCreateRequest) async throws -> Habit {
        let response: APIResponse<Habit> = try await performRequest(
            endpoint: .createHabit,
            method: .POST,
            body: request,
            requiresAuth: true
        )
        return response.data
    }
    
    func getHabit(_ habitId: String) async throws -> Habit {
        let response: APIResponse<Habit> = try await performRequest(
            endpoint: .getHabit(habitId),
            method: .GET,
            requiresAuth: true
        )
        return response.data
    }
    
    func updateHabit(_ habitId: String, _ request: HabitUpdateRequest) async throws -> Habit {
        let response: APIResponse<Habit> = try await performRequest(
            endpoint: .updateHabit(habitId),
            method: .PUT,
            body: request,
            requiresAuth: true
        )
        return response.data
    }
    
    func deleteHabit(_ habitId: String) async throws {
        try await performRequest(
            endpoint: .deleteHabit(habitId),
            method: .DELETE,
            requiresAuth: true
        )
    }
    
    func recordHabitCompletion(habitId: String, request: HabitRecordRequest) async throws -> HabitRecord {
        let response: APIResponse<HabitRecord> = try await performRequest(
            endpoint: .recordHabitCompletion(habitId),
            method: .POST,
            body: request,
            requiresAuth: true
        )
        return response.data
    }
    
    // MARK: - Private Methods
    
    private func performRequest<T: Codable>(
        endpoint: APIEndpoint,
        method: HTTPMethod,
        queryItems: [URLQueryItem]? = nil,
        body: Codable? = nil,
        requiresAuth: Bool = false
    ) async throws -> T {
        let request = try buildRequest(
            endpoint: endpoint,
            method: method,
            queryItems: queryItems,
            body: body,
            requiresAuth: requiresAuth
        )
        
        return try await executeRequest(request)
    }
    
    private func performRequest(
        endpoint: APIEndpoint,
        method: HTTPMethod,
        queryItems: [URLQueryItem]? = nil,
        body: Codable? = nil,
        requiresAuth: Bool = false
    ) async throws {
        let request = try buildRequest(
            endpoint: endpoint,
            method: method,
            queryItems: queryItems,
            body: body,
            requiresAuth: requiresAuth
        )
        
        let _: EmptyResponse = try await executeRequest(request)
    }
    
    private func buildRequest(
        endpoint: APIEndpoint,
        method: HTTPMethod,
        queryItems: [URLQueryItem]?,
        body: Codable?,
        requiresAuth: Bool
    ) throws -> URLRequest {
        var url = baseURL.appendingPathComponent(endpoint.path)
        
        if let queryItems = queryItems, !queryItems.isEmpty {
            var components = URLComponents(url: url, resolvingAgainstBaseURL: false)
            components?.queryItems = queryItems
            url = components?.url ?? url
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        
        if requiresAuth {
            guard let token = authToken else {
                throw APIError.unauthorized
            }
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        if let body = body {
            request.httpBody = try JSONEncoder().encode(body)
        }
        
        return request
    }
    
    private func executeRequest<T: Codable>(_ request: URLRequest) async throws -> T {
        do {
            let (data, response) = try await session.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.invalidResponse
            }
            
            switch httpResponse.statusCode {
            case 200...299:
                if T.self == EmptyResponse.self {
                    return EmptyResponse() as! T
                }
                
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                
                return try decoder.decode(T.self, from: data)
                
            case 401:
                throw APIError.unauthorized
            case 403:
                throw APIError.forbidden
            case 404:
                throw APIError.notFound
            case 429:
                throw APIError.rateLimitExceeded
            case 500...599:
                throw APIError.serverError
            default:
                throw APIError.unknown(httpResponse.statusCode)
            }
            
        } catch {
            if error is APIError {
                throw error
            } else {
                throw APIError.networkError(error)
            }
        }
    }
}

// MARK: - Supporting Types

enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
}

enum APIEndpoint {
    // Auth
    case signUp
    case signIn
    case signInWithApple
    case signOut
    case forgotPassword
    
    // User
    case getCurrentUser
    case updateUser
    case changePassword
    case getUserStatistics
    
    // Tasks
    case getTasks
    case createTask
    case getTask(String)
    case updateTask(String)
    case deleteTask(String)
    case batchUpdateTasks
    case getTaskStatistics
    
    // Habits
    case getHabits
    case createHabit
    case getHabit(String)
    case updateHabit(String)
    case deleteHabit(String)
    case recordHabitCompletion(String)
    
    var path: String {
        switch self {
        // Auth
        case .signUp:
            return "auth/register"
        case .signIn:
            return "auth/login"
        case .signInWithApple:
            return "auth/apple"
        case .signOut:
            return "auth/logout"
        case .forgotPassword:
            return "auth/forgot-password"
            
        // User
        case .getCurrentUser:
            return "users/me"
        case .updateUser:
            return "users/me"
        case .changePassword:
            return "users/change-password"
        case .getUserStatistics:
            return "users/me/stats"
            
        // Tasks
        case .getTasks:
            return "tasks"
        case .createTask:
            return "tasks"
        case .getTask(let id):
            return "tasks/\(id)"
        case .updateTask(let id):
            return "tasks/\(id)"
        case .deleteTask(let id):
            return "tasks/\(id)"
        case .batchUpdateTasks:
            return "tasks/batch"
        case .getTaskStatistics:
            return "tasks/statistics"
            
        // Habits
        case .getHabits:
            return "habits"
        case .createHabit:
            return "habits"
        case .getHabit(let id):
            return "habits/\(id)"
        case .updateHabit(let id):
            return "habits/\(id)"
        case .deleteHabit(let id):
            return "habits/\(id)"
        case .recordHabitCompletion(let id):
            return "habits/\(id)/records"
        }
    }
}

enum APIError: LocalizedError {
    case unauthorized
    case forbidden
    case notFound
    case rateLimitExceeded
    case serverError
    case networkError(Error)
    case invalidResponse
    case unknown(Int)
    
    var errorDescription: String? {
        switch self {
        case .unauthorized:
            return "未授权访问"
        case .forbidden:
            return "访问被禁止"
        case .notFound:
            return "资源未找到"
        case .rateLimitExceeded:
            return "请求过于频繁，请稍后重试"
        case .serverError:
            return "服务器错误"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .invalidResponse:
            return "无效的服务器响应"
        case .unknown(let code):
            return "未知错误 (状态码: \(code))"
        }
    }
}

struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let data: T
    let message: String?
}

struct EmptyResponse: Codable {
    init() {}
}
