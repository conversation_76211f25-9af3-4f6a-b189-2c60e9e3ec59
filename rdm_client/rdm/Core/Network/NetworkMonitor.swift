//
//  NetworkMonitor.swift
//  TaskFlow
//
//  Created by TaskFlow Team on 2024/01/01.
//

import Foundation
import Network
import Combine

/// 网络状态监控
@MainActor
class NetworkMonitor: ObservableObject {
    static let shared = NetworkMonitor()
    
    // MARK: - Published Properties
    
    @Published var status: NetworkStatus = .unknown
    @Published var isConnected = false
    @Published var connectionType: ConnectionType = .unknown
    
    // MARK: - Private Properties
    
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    private var isMonitoring = false
    
    // MARK: - Initialization
    
    private init() {
        setupMonitor()
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - Public Methods
    
    /// 开始监控网络状态
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        monitor.start(queue: queue)
        isMonitoring = true
    }
    
    /// 停止监控网络状态
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        monitor.cancel()
        isMonitoring = false
    }
    
    /// 检查网络连接状态
    func checkConnection() async -> Bool {
        return await withCheckedContinuation { continuation in
            let testMonitor = NWPathMonitor()
            testMonitor.pathUpdateHandler = { path in
                testMonitor.cancel()
                continuation.resume(returning: path.status == .satisfied)
            }
            testMonitor.start(queue: DispatchQueue.global())
        }
    }
    
    // MARK: - Private Methods
    
    private func setupMonitor() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.updateNetworkStatus(path)
            }
        }
    }
    
    private func updateNetworkStatus(_ path: NWPath) {
        let newIsConnected = path.status == .satisfied
        let newConnectionType = determineConnectionType(path)
        let newStatus = determineNetworkStatus(isConnected: newIsConnected, connectionType: newConnectionType)
        
        // 只在状态真正改变时更新
        if status != newStatus || isConnected != newIsConnected || connectionType != newConnectionType {
            status = newStatus
            isConnected = newIsConnected
            connectionType = newConnectionType
            
            // 发送网络状态变化通知
            NotificationCenter.default.post(
                name: .networkStatusChanged,
                object: nil,
                userInfo: [
                    "status": status,
                    "isConnected": isConnected,
                    "connectionType": connectionType
                ]
            )
        }
    }
    
    private func determineConnectionType(_ path: NWPath) -> ConnectionType {
        if path.usesInterfaceType(.wifi) {
            return .wifi
        } else if path.usesInterfaceType(.cellular) {
            return .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            return .ethernet
        } else {
            return .unknown
        }
    }
    
    private func determineNetworkStatus(isConnected: Bool, connectionType: ConnectionType) -> NetworkStatus {
        if isConnected {
            return .reachable(connectionType)
        } else {
            return .notReachable
        }
    }
}

// MARK: - Supporting Types

enum NetworkStatus: Equatable {
    case unknown
    case notReachable
    case reachable(ConnectionType)
    
    var isConnected: Bool {
        switch self {
        case .reachable:
            return true
        default:
            return false
        }
    }
    
    var description: String {
        switch self {
        case .unknown:
            return "未知"
        case .notReachable:
            return "无网络连接"
        case .reachable(let type):
            return "已连接 (\(type.description))"
        }
    }
}

enum ConnectionType: Equatable {
    case unknown
    case wifi
    case cellular
    case ethernet
    
    var description: String {
        switch self {
        case .unknown:
            return "未知"
        case .wifi:
            return "Wi-Fi"
        case .cellular:
            return "蜂窝网络"
        case .ethernet:
            return "以太网"
        }
    }
    
    var iconName: String {
        switch self {
        case .unknown:
            return "questionmark.circle"
        case .wifi:
            return "wifi"
        case .cellular:
            return "antenna.radiowaves.left.and.right"
        case .ethernet:
            return "cable.connector"
        }
    }
    
    var isExpensive: Bool {
        switch self {
        case .cellular:
            return true
        default:
            return false
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let networkStatusChanged = Notification.Name("NetworkStatusChanged")
}

// MARK: - Network Monitor Extensions

extension NetworkMonitor {
    /// 是否为高速网络连接
    var isHighSpeedConnection: Bool {
        switch connectionType {
        case .wifi, .ethernet:
            return true
        case .cellular, .unknown:
            return false
        }
    }
    
    /// 是否为计费网络连接
    var isExpensiveConnection: Bool {
        return connectionType.isExpensive
    }
    
    /// 网络状态描述
    var statusDescription: String {
        return status.description
    }
    
    /// 连接类型图标
    var connectionIcon: String {
        return connectionType.iconName
    }
}

// MARK: - Network Quality Assessment

extension NetworkMonitor {
    /// 网络质量评估
    enum NetworkQuality {
        case excellent
        case good
        case fair
        case poor
        case offline
        
        var description: String {
            switch self {
            case .excellent:
                return "优秀"
            case .good:
                return "良好"
            case .fair:
                return "一般"
            case .poor:
                return "较差"
            case .offline:
                return "离线"
            }
        }
        
        var color: String {
            switch self {
            case .excellent:
                return "green"
            case .good:
                return "blue"
            case .fair:
                return "orange"
            case .poor:
                return "red"
            case .offline:
                return "gray"
            }
        }
    }
    
    /// 评估网络质量
    func assessNetworkQuality() async -> NetworkQuality {
        guard isConnected else { return .offline }
        
        // 简单的网络质量评估
        // 实际应用中可以通过ping测试、下载速度测试等方式来评估
        switch connectionType {
        case .wifi, .ethernet:
            return .excellent
        case .cellular:
            return .good
        case .unknown:
            return .fair
        }
    }
    
    /// 执行网络连接测试
    func performConnectivityTest() async -> Bool {
        guard isConnected else { return false }
        
        // 尝试连接到一个可靠的服务器
        guard let url = URL(string: "https://www.apple.com") else { return false }
        
        do {
            let (_, response) = try await URLSession.shared.data(from: url)
            if let httpResponse = response as? HTTPURLResponse {
                return httpResponse.statusCode == 200
            }
            return false
        } catch {
            return false
        }
    }
}

// MARK: - Reachability Helper

extension NetworkMonitor {
    /// 等待网络连接恢复
    func waitForConnection(timeout: TimeInterval = 30) async -> Bool {
        if isConnected {
            return true
        }
        
        return await withCheckedContinuation { continuation in
            var hasResumed = false
            
            // 设置超时
            DispatchQueue.main.asyncAfter(deadline: .now() + timeout) {
                if !hasResumed {
                    hasResumed = true
                    continuation.resume(returning: false)
                }
            }
            
            // 监听网络状态变化
            let cancellable = $isConnected
                .filter { $0 }
                .first()
                .sink { _ in
                    if !hasResumed {
                        hasResumed = true
                        continuation.resume(returning: true)
                    }
                }
            
            // 确保在完成时取消订阅
            DispatchQueue.main.asyncAfter(deadline: .now() + timeout + 1) {
                cancellable.cancel()
            }
        }
    }
    
    /// 在网络可用时执行操作
    func performWhenConnected<T>(
        timeout: TimeInterval = 30,
        operation: @escaping () async throws -> T
    ) async throws -> T {
        if isConnected {
            return try await operation()
        }
        
        let connected = await waitForConnection(timeout: timeout)
        guard connected else {
            throw NetworkError.timeout
        }
        
        return try await operation()
    }
}

// MARK: - Network Error

enum NetworkError: LocalizedError {
    case timeout
    case notConnected
    case connectionLost
    
    var errorDescription: String? {
        switch self {
        case .timeout:
            return "网络连接超时"
        case .notConnected:
            return "无网络连接"
        case .connectionLost:
            return "网络连接中断"
        }
    }
}
