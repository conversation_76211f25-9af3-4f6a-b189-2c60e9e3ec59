# TaskFlow 开发规范指南
## 代码规范、工作流程与项目管理

### 1. 项目目录结构

#### 1.1 iOS项目结构
```
TaskFlow/
├── App/
│   ├── TaskFlowApp.swift              # 应用入口
│   ├── ContentView.swift              # 根视图
│   └── AppDelegate.swift              # 应用代理
├── Core/
│   ├── Models/                        # 数据模型
│   │   ├── Task.swift
│   │   ├── Habit.swift
│   │   └── User.swift
│   ├── Services/                      # 业务服务
│   │   ├── TaskService.swift
│   │   ├── HabitService.swift
│   │   └── AuthService.swift
│   ├── Repositories/                  # 数据仓库
│   │   ├── TaskRepository.swift
│   │   └── HabitRepository.swift
│   └── Network/                       # 网络层
│       ├── APIClient.swift
│       ├── NetworkManager.swift
│       └── Endpoints.swift
├── Features/                          # 功能模块
│   ├── Tasks/
│   │   ├── Views/
│   │   ├── ViewModels/
│   │   └── Models/
│   ├── Habits/
│   │   ├── Views/
│   │   ├── ViewModels/
│   │   └── Models/
│   ├── Auth/
│   └── Settings/
├── Shared/                           # 共享组件
│   ├── Components/                   # UI组件
│   ├── Extensions/                   # 扩展
│   ├── Utils/                        # 工具类
│   └── Constants/                    # 常量定义
├── Resources/                        # 资源文件
│   ├── Assets.xcassets
│   ├── Localizable.strings
│   └── Info.plist
└── Tests/                           # 测试文件
    ├── UnitTests/
    ├── IntegrationTests/
    └── UITests/
```

#### 1.2 后端项目结构
```
taskflow-server/
├── src/
│   ├── controllers/                  # 控制器
│   │   ├── taskController.js
│   │   ├── habitController.js
│   │   └── authController.js
│   ├── models/                       # 数据模型
│   │   ├── Task.js
│   │   ├── Habit.js
│   │   └── User.js
│   ├── services/                     # 业务逻辑
│   │   ├── taskService.js
│   │   └── habitService.js
│   ├── middleware/                   # 中间件
│   │   ├── auth.js
│   │   ├── validation.js
│   │   └── errorHandler.js
│   ├── routes/                       # 路由定义
│   │   ├── tasks.js
│   │   ├── habits.js
│   │   └── auth.js
│   ├── utils/                        # 工具函数
│   └── config/                       # 配置文件
├── tests/                           # 测试文件
├── docs/                            # 文档
├── package.json
└── server.js                        # 服务器入口
```

### 2. Swift 代码规范

#### 2.1 命名规范
```swift
// 类名：大驼峰命名法
class TaskListViewModel: ObservableObject { }

// 变量和函数：小驼峰命名法
var isCompleted: Bool = false
func fetchTasks() async throws -> [Task] { }

// 常量：小驼峰命名法
let maxTaskCount = 100
static let defaultTimeout: TimeInterval = 30

// 枚举：大驼峰命名法，值使用小驼峰
enum TaskPriority {
    case high
    case medium
    case low
}

// 协议：使用 -able 或 -ing 后缀
protocol TaskManageable { }
protocol NetworkRequesting { }
```

#### 2.2 代码格式规范
```swift
// 函数参数换行
func createTask(
    title: String,
    description: String?,
    priority: TaskPriority,
    dueDate: Date?
) async throws -> Task {
    // 实现
}

// 闭包格式
tasks.filter { task in
    task.isCompleted == false
}.map { task in
    task.title
}

// 简化闭包
tasks.filter(\.isCompleted).map(\.title)

// Guard 语句
guard let title = task.title,
      !title.isEmpty else {
    throw TaskError.invalidTitle
}
```

#### 2.3 注释规范
```swift
/// 任务管理服务
/// 
/// 提供任务的创建、更新、删除和查询功能
/// 支持本地存储和云端同步
class TaskService {
    
    /// 创建新任务
    /// - Parameters:
    ///   - title: 任务标题
    ///   - description: 任务描述（可选）
    ///   - priority: 任务优先级
    /// - Returns: 创建的任务对象
    /// - Throws: TaskError 如果创建失败
    func createTask(
        title: String,
        description: String? = nil,
        priority: TaskPriority = .medium
    ) async throws -> Task {
        // TODO: 实现任务创建逻辑
        // FIXME: 需要添加输入验证
        // MARK: - 核心业务逻辑
    }
}
```

### 3. JavaScript/Node.js 代码规范

#### 3.1 命名和格式规范
```javascript
// 变量和函数：小驼峰命名法
const taskService = new TaskService();
const isCompleted = true;

function createTask(taskData) {
  // 实现
}

// 常量：大写下划线
const MAX_TASK_COUNT = 100;
const API_BASE_URL = 'https://api.taskflow.app';

// 类名：大驼峰命名法
class TaskController {
  constructor() {
    this.taskService = new TaskService();
  }
  
  async createTask(req, res) {
    try {
      const task = await this.taskService.create(req.body);
      res.status(201).json({ success: true, data: task });
    } catch (error) {
      res.status(400).json({ 
        success: false, 
        error: error.message 
      });
    }
  }
}
```

#### 3.2 异步处理规范
```javascript
// 使用 async/await
async function fetchTasks(userId) {
  try {
    const tasks = await Task.find({ userId });
    return tasks;
  } catch (error) {
    logger.error('Failed to fetch tasks:', error);
    throw new Error('Unable to fetch tasks');
  }
}

// 错误处理
const handleAsyncRoute = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// 使用示例
router.get('/tasks', handleAsyncRoute(async (req, res) => {
  const tasks = await taskService.fetchTasks(req.user.id);
  res.json({ success: true, data: tasks });
}));
```

### 4. Git 工作流程

#### 4.1 分支策略
```bash
# 主分支
main                    # 生产环境代码
develop                 # 开发环境代码

# 功能分支
feature/task-management # 任务管理功能
feature/habit-tracking  # 习惯追踪功能
feature/user-auth      # 用户认证功能

# 修复分支
hotfix/critical-bug    # 紧急修复
bugfix/task-creation   # 一般bug修复

# 发布分支
release/v1.0.0         # 版本发布准备
```

#### 4.2 提交信息规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

<body>

<footer>

# 类型说明
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式调整
refactor: 重构代码
test:     测试相关
chore:    构建工具或辅助工具的变动

# 示例
feat(tasks): add task creation functionality

- Implement task creation form
- Add validation for required fields
- Integrate with backend API

Closes #123
```

#### 4.3 代码审查流程
```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发和提交
git add .
git commit -m "feat: implement new feature"

# 3. 推送到远程
git push origin feature/new-feature

# 4. 创建 Pull Request
# 5. 代码审查
# 6. 合并到 develop 分支
# 7. 删除功能分支
```

### 5. 测试策略

#### 5.1 iOS 测试规范
```swift
// 单元测试示例
import XCTest
@testable import TaskFlow

class TaskServiceTests: XCTestCase {
    var taskService: TaskService!
    
    override func setUp() {
        super.setUp()
        taskService = TaskService()
    }
    
    override func tearDown() {
        taskService = nil
        super.tearDown()
    }
    
    func testCreateTask() async throws {
        // Given
        let title = "Test Task"
        let priority = TaskPriority.high
        
        // When
        let task = try await taskService.createTask(
            title: title,
            priority: priority
        )
        
        // Then
        XCTAssertEqual(task.title, title)
        XCTAssertEqual(task.priority, priority)
        XCTAssertFalse(task.isCompleted)
    }
}

// UI测试示例
class TaskFlowUITests: XCTestCase {
    func testTaskCreation() {
        let app = XCUIApplication()
        app.launch()
        
        // 点击添加按钮
        app.buttons["add_task_button"].tap()
        
        // 输入任务标题
        let titleField = app.textFields["task_title_field"]
        titleField.tap()
        titleField.typeText("New Task")
        
        // 保存任务
        app.buttons["save_task_button"].tap()
        
        // 验证任务已创建
        XCTAssertTrue(app.staticTexts["New Task"].exists)
    }
}
```

#### 5.2 后端测试规范
```javascript
// 单元测试示例 (Jest)
const TaskService = require('../src/services/taskService');
const Task = require('../src/models/Task');

describe('TaskService', () => {
  let taskService;
  
  beforeEach(() => {
    taskService = new TaskService();
  });
  
  describe('createTask', () => {
    it('should create a task with valid data', async () => {
      // Arrange
      const taskData = {
        title: 'Test Task',
        priority: 'high',
        userId: 'user123'
      };
      
      // Act
      const task = await taskService.create(taskData);
      
      // Assert
      expect(task.title).toBe(taskData.title);
      expect(task.priority).toBe(taskData.priority);
      expect(task.isCompleted).toBe(false);
    });
    
    it('should throw error for invalid data', async () => {
      // Arrange
      const invalidData = { title: '' };
      
      // Act & Assert
      await expect(taskService.create(invalidData))
        .rejects.toThrow('Title is required');
    });
  });
});

// 集成测试示例
const request = require('supertest');
const app = require('../src/app');

describe('Tasks API', () => {
  describe('POST /api/tasks', () => {
    it('should create a new task', async () => {
      const taskData = {
        title: 'Integration Test Task',
        priority: 'medium'
      };
      
      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', 'Bearer valid-token')
        .send(taskData)
        .expect(201);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data.title).toBe(taskData.title);
    });
  });
});
```

### 6. CI/CD 流程

#### 6.1 GitHub Actions 配置
```yaml
# .github/workflows/ios.yml
name: iOS CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: macos-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable
    
    - name: Install dependencies
      run: |
        cd ios
        pod install
    
    - name: Run tests
      run: |
        cd ios
        xcodebuild test \
          -workspace TaskFlow.xcworkspace \
          -scheme TaskFlow \
          -destination 'platform=iOS Simulator,name=iPhone 14'
    
    - name: Build for release
      if: github.ref == 'refs/heads/main'
      run: |
        cd ios
        xcodebuild archive \
          -workspace TaskFlow.xcworkspace \
          -scheme TaskFlow \
          -archivePath TaskFlow.xcarchive
```

#### 6.2 代码质量检查
```yaml
# 代码质量检查
- name: SwiftLint
  run: |
    swiftlint lint --reporter github-actions-logging

- name: Code Coverage
  run: |
    xcodebuild test \
      -enableCodeCoverage YES \
      -workspace TaskFlow.xcworkspace \
      -scheme TaskFlow \
      -destination 'platform=iOS Simulator,name=iPhone 14'
```

### 7. 性能优化指南

#### 7.1 iOS 性能优化
```swift
// 内存优化
class ImageCache {
    private let cache = NSCache<NSString, UIImage>()
    
    init() {
        cache.totalCostLimit = 50 * 1024 * 1024 // 50MB
        cache.countLimit = 100
    }
}

// 网络请求优化
class NetworkManager {
    private let session: URLSession
    
    init() {
        let config = URLSessionConfiguration.default
        config.requestCachePolicy = .returnCacheDataElseLoad
        config.timeoutIntervalForRequest = 30
        self.session = URLSession(configuration: config)
    }
}

// UI 性能优化
struct OptimizedList: View {
    let items: [Item]
    
    var body: some View {
        LazyVStack {
            ForEach(items) { item in
                ItemRow(item: item)
                    .onAppear {
                        // 预加载逻辑
                    }
            }
        }
    }
}
```

### 8. 安全开发规范

#### 8.1 数据安全
```swift
// Keychain 存储敏感信息
class KeychainManager {
    static func save(key: String, data: Data) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        SecItemDelete(query as CFDictionary)
        return SecItemAdd(query as CFDictionary, nil) == errSecSuccess
    }
}

// 网络请求安全
class SecureAPIClient {
    func makeRequest<T: Codable>(endpoint: Endpoint) async throws -> T {
        var request = URLRequest(url: endpoint.url)
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(getAuthToken())", forHTTPHeaderField: "Authorization")
        
        // 证书固定
        let session = URLSession(
            configuration: .default,
            delegate: self,
            delegateQueue: nil
        )
        
        let (data, _) = try await session.data(for: request)
        return try JSONDecoder().decode(T.self, from: data)
    }
}
```

### 9. 文档规范

#### 9.1 代码文档
- 所有公开API必须有完整的文档注释
- 复杂算法需要详细的实现说明
- 重要的业务逻辑需要注释说明

#### 9.2 README 文档
```markdown
# TaskFlow

## 项目简介
TaskFlow 是一款专注于个人效率提升的任务管理和习惯追踪应用。

## 技术栈
- iOS: Swift 5.9, SwiftUI, Combine
- 后端: Node.js, Express, Parse Server
- 数据库: MongoDB

## 快速开始
1. 克隆项目
2. 安装依赖
3. 配置环境变量
4. 运行项目

## 贡献指南
请阅读 CONTRIBUTING.md 了解详细的贡献流程。
```

### 10. 发布流程

#### 10.1 版本管理
```bash
# 语义化版本控制
MAJOR.MINOR.PATCH

# 示例
1.0.0  # 初始版本
1.0.1  # 修复bug
1.1.0  # 新增功能
2.0.0  # 重大更新
```

#### 10.2 App Store 发布清单
- [ ] 功能测试完成
- [ ] 性能测试通过
- [ ] 隐私政策更新
- [ ] 应用截图准备
- [ ] 应用描述撰写
- [ ] 版本说明编写
- [ ] 审核指南检查
- [ ] 测试设备验证

---

*文档版本: v1.0*  
*创建日期: 2024年*  
*最后更新: 2024年*
