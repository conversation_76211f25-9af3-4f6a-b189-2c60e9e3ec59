# TaskFlow UI/UX 设计规范
## 用户界面与用户体验设计指南

### 1. 设计系统概览

#### 1.1 设计理念
- **简洁至上**: 专注核心功能，避免界面冗余
- **直觉操作**: 符合iOS用户习惯，降低学习成本
- **情感化设计**: 通过颜色和动画传达积极情感
- **无障碍友好**: 支持VoiceOver和动态字体

#### 1.2 设计原则
1. **一致性**: 统一的视觉语言和交互模式
2. **可用性**: 易于理解和操作的界面
3. **美观性**: 现代化的视觉设计
4. **响应性**: 适配不同设备尺寸
5. **性能**: 流畅的动画和快速响应

### 2. 颜色系统

#### 2.1 主色调 (Primary Colors)
```swift
// 主品牌色
static let primaryBlue = Color(hex: "#007AFF")      // iOS系统蓝
static let primaryGreen = Color(hex: "#34C759")     // 成功绿色
static let primaryRed = Color(hex: "#FF3B30")       // 警告红色
static let primaryOrange = Color(hex: "#FF9500")    // 提醒橙色

// 辅助色
static let secondaryBlue = Color(hex: "#5AC8FA")    // 浅蓝色
static let secondaryGray = Color(hex: "#8E8E93")    // 中性灰
static let accentPurple = Color(hex: "#AF52DE")     // 强调紫色
```

#### 2.2 语义化颜色 (Semantic Colors)
```swift
// 功能性颜色
static let successColor = primaryGreen
static let warningColor = primaryOrange  
static let errorColor = primaryRed
static let infoColor = primaryBlue

// 优先级颜色
static let highPriority = primaryRed
static let mediumPriority = primaryOrange
static let lowPriority = secondaryGray
```

#### 2.3 深色模式适配
```swift
// 自适应颜色
static let backgroundColor = Color(.systemBackground)
static let secondaryBackground = Color(.secondarySystemBackground)
static let textPrimary = Color(.label)
static let textSecondary = Color(.secondaryLabel)
static let separator = Color(.separator)
```

### 3. 字体系统

#### 3.1 字体层级
```swift
// 标题字体
static let largeTitle = Font.largeTitle.weight(.bold)      // 34pt
static let title1 = Font.title.weight(.semibold)          // 28pt
static let title2 = Font.title2.weight(.semibold)         // 22pt
static let title3 = Font.title3.weight(.medium)           // 20pt

// 正文字体
static let headline = Font.headline.weight(.semibold)      // 17pt
static let body = Font.body                                // 17pt
static let callout = Font.callout                          // 16pt
static let subheadline = Font.subheadline                  // 15pt

// 辅助字体
static let footnote = Font.footnote                        // 13pt
static let caption1 = Font.caption                         // 12pt
static let caption2 = Font.caption2                        // 11pt
```

#### 3.2 字体使用规范
- **标题**: 使用San Francisco字体，支持动态字体
- **正文**: 保持17pt基准大小，确保可读性
- **数字**: 使用等宽数字字体，便于对齐
- **多语言**: 支持中文字体优化显示

### 4. 间距系统

#### 4.1 间距规范
```swift
// 基础间距单位 (8pt网格系统)
static let spacing2: CGFloat = 2    // 微小间距
static let spacing4: CGFloat = 4    // 最小间距
static let spacing8: CGFloat = 8    // 小间距
static let spacing12: CGFloat = 12  // 中小间距
static let spacing16: CGFloat = 16  // 标准间距
static let spacing20: CGFloat = 20  // 中等间距
static let spacing24: CGFloat = 24  // 大间距
static let spacing32: CGFloat = 32  // 很大间距
static let spacing40: CGFloat = 40  // 超大间距
```

#### 4.2 布局间距应用
- **组件内边距**: 16pt
- **组件间距**: 12pt
- **页面边距**: 20pt
- **分组间距**: 24pt
- **页面顶部安全区**: 动态适配

### 5. 组件库设计

#### 5.1 按钮组件
```swift
// 主要按钮
struct PrimaryButton: View {
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(Color.primaryBlue)
                .cornerRadius(12)
        }
    }
}

// 次要按钮
struct SecondaryButton: View {
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.headline)
                .foregroundColor(.primaryBlue)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.primaryBlue, lineWidth: 2)
                )
        }
    }
}
```

#### 5.2 卡片组件
```swift
struct TaskCard: View {
    let task: Task
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(task.title)
                    .font(.headline)
                    .foregroundColor(.textPrimary)
                Spacer()
                PriorityIndicator(priority: task.priority)
            }
            
            if let description = task.description {
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.textSecondary)
                    .lineLimit(2)
            }
            
            HStack {
                if let dueDate = task.dueDate {
                    Label(dueDate.formatted(), systemImage: "calendar")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }
                Spacer()
                TaskStatusButton(task: task)
            }
        }
        .padding(16)
        .background(Color.secondaryBackground)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}
```

#### 5.3 输入组件
```swift
struct TaskTextField: View {
    @Binding var text: String
    let placeholder: String
    
    var body: some View {
        TextField(placeholder, text: $text)
            .font(.body)
            .padding(16)
            .background(Color.secondaryBackground)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.separator, lineWidth: 1)
            )
    }
}
```

### 6. 图标系统

#### 6.1 图标规范
- **系统图标**: 优先使用SF Symbols
- **自定义图标**: 24x24pt标准尺寸
- **线条粗细**: 2pt标准线宽
- **圆角**: 4pt圆角半径

#### 6.2 常用图标定义
```swift
enum AppIcons {
    // 导航图标
    static let tasks = "checkmark.circle"
    static let habits = "repeat.circle"
    static let statistics = "chart.bar"
    static let settings = "gear"
    
    // 操作图标
    static let add = "plus"
    static let edit = "pencil"
    static let delete = "trash"
    static let share = "square.and.arrow.up"
    
    // 状态图标
    static let completed = "checkmark.circle.fill"
    static let pending = "circle"
    static let overdue = "exclamationmark.triangle"
    
    // 优先级图标
    static let highPriority = "exclamationmark.3"
    static let mediumPriority = "exclamationmark.2"
    static let lowPriority = "minus"
}
```

### 7. 动画设计

#### 7.1 动画原则
- **有意义**: 动画应该有明确的功能目的
- **自然**: 遵循物理规律，避免突兀
- **快速**: 动画时长控制在0.2-0.5秒
- **一致**: 相同操作使用相同动画

#### 7.2 动画实现
```swift
// 标准缓动动画
static let standardAnimation = Animation.easeInOut(duration: 0.3)

// 弹性动画
static let springAnimation = Animation.spring(
    response: 0.5,
    dampingFraction: 0.8,
    blendDuration: 0
)

// 页面转场动画
struct SlideTransition: ViewModifier {
    let isPresented: Bool
    
    func body(content: Content) -> some View {
        content
            .offset(x: isPresented ? 0 : UIScreen.main.bounds.width)
            .animation(.easeInOut(duration: 0.3), value: isPresented)
    }
}
```

### 8. 用户交互流程

#### 8.1 任务管理流程
```
启动应用 → 任务列表 → 创建/编辑任务 → 保存 → 返回列表
         ↓
    点击任务 → 任务详情 → 标记完成/编辑 → 更新状态
```

#### 8.2 习惯追踪流程
```
习惯列表 → 创建习惯 → 设置参数 → 保存
         ↓
    日常打卡 → 选择习惯 → 记录完成 → 更新统计
```

#### 8.3 导航结构
```swift
TabView {
    TaskListView()
        .tabItem {
            Image(systemName: AppIcons.tasks)
            Text("任务")
        }
    
    HabitListView()
        .tabItem {
            Image(systemName: AppIcons.habits)
            Text("习惯")
        }
    
    StatisticsView()
        .tabItem {
            Image(systemName: AppIcons.statistics)
            Text("统计")
        }
    
    SettingsView()
        .tabItem {
            Image(systemName: AppIcons.settings)
            Text("设置")
        }
}
```

### 9. 响应式设计

#### 9.1 设备适配
```swift
// 屏幕尺寸适配
struct AdaptiveLayout: View {
    @Environment(\.horizontalSizeClass) var horizontalSizeClass
    
    var body: some View {
        if horizontalSizeClass == .compact {
            // iPhone 竖屏布局
            VStack { /* 内容 */ }
        } else {
            // iPad 或 iPhone 横屏布局
            HStack { /* 内容 */ }
        }
    }
}
```

#### 9.2 动态字体支持
```swift
struct DynamicText: View {
    let text: String
    
    var body: some View {
        Text(text)
            .font(.body)
            .minimumScaleFactor(0.8)
            .lineLimit(nil)
    }
}
```

### 10. 无障碍设计

#### 10.1 VoiceOver支持
```swift
struct AccessibleTaskRow: View {
    let task: Task
    
    var body: some View {
        HStack {
            Text(task.title)
            Spacer()
            Button("标记完成") {
                // 完成任务
            }
            .accessibilityLabel("标记任务\(task.title)为已完成")
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(task.title), 优先级\(task.priority)")
        .accessibilityHint("双击查看任务详情")
    }
}
```

#### 10.2 颜色对比度
- **正常文本**: 对比度 ≥ 4.5:1
- **大文本**: 对比度 ≥ 3:1
- **图标**: 对比度 ≥ 3:1

### 11. 设计交付规范

#### 11.1 设计文件组织
```
Design/
├── Assets/
│   ├── Icons/
│   ├── Images/
│   └── Colors/
├── Prototypes/
│   ├── User-Flow.fig
│   └── Interactive-Prototype.fig
└── Specifications/
    ├── Component-Library.fig
    └── Design-System.fig
```

#### 11.2 开发交付清单
- [ ] 完整的组件库文件
- [ ] 颜色和字体规范文档
- [ ] 图标资源文件 (SVG/PDF)
- [ ] 交互原型演示
- [ ] 无障碍设计说明
- [ ] 响应式布局规范

---

*文档版本: v1.0*  
*创建日期: 2024年*  
*最后更新: 2024年*
