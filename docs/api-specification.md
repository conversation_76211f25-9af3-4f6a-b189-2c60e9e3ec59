# TaskFlow API 规格说明
## RESTful API 接口定义

### 1. API 基础信息

#### 1.1 基本配置
- **Base URL**: `https://api.taskflow.app/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1.0

#### 1.2 通用响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "uuid-string"
}
```

#### 1.3 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入参数验证失败",
    "details": {
      "field": "title",
      "reason": "标题不能为空"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "uuid-string"
}
```

### 2. 认证机制

#### 2.1 认证方式
- **Bearer Token**: 用于API访问认证
- **Apple ID**: 支持Apple ID第三方登录
- **Session Token**: Parse Server会话管理

#### 2.2 请求头要求
```http
Authorization: Bearer <access_token>
Content-Type: application/json
X-Parse-Application-Id: <app_id>
X-Parse-REST-API-Key: <api_key>
```

#### 2.3 认证相关端点

**用户注册**
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "username": "username"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user": {
      "objectId": "user_id",
      "email": "<EMAIL>",
      "username": "username",
      "createdAt": "2024-01-01T00:00:00Z"
    },
    "sessionToken": "session_token_string"
  }
}
```

**用户登录**
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Apple ID 登录**
```http
POST /auth/apple
Content-Type: application/json

{
  "identityToken": "apple_identity_token",
  "authorizationCode": "apple_auth_code",
  "user": {
    "name": {
      "firstName": "John",
      "lastName": "Doe"
    },
    "email": "<EMAIL>"
  }
}
```

### 3. 任务管理 API

#### 3.1 获取任务列表
```http
GET /tasks?page=1&limit=20&status=active&priority=high
Authorization: Bearer <token>
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)
- `status`: 任务状态 (active, completed, all)
- `priority`: 优先级 (high, medium, low)
- `dueDate`: 截止日期筛选 (today, tomorrow, week)

**响应:**
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "objectId": "task_id",
        "title": "完成项目文档",
        "description": "编写技术文档和用户手册",
        "isCompleted": false,
        "priority": "high",
        "dueDate": "2024-01-15T09:00:00Z",
        "category": "工作",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "totalPages": 3
    }
  }
}
```

#### 3.2 创建任务
```http
POST /tasks
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "新任务标题",
  "description": "任务描述",
  "priority": "medium",
  "dueDate": "2024-01-15T09:00:00Z",
  "category": "个人"
}
```

**验证规则:**
- `title`: 必填，1-100字符
- `description`: 可选，最大500字符
- `priority`: 枚举值 (high, medium, low)
- `dueDate`: ISO 8601格式日期
- `category`: 可选，最大50字符

#### 3.3 更新任务
```http
PUT /tasks/{taskId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "更新后的标题",
  "isCompleted": true,
  "priority": "low"
}
```

#### 3.4 删除任务
```http
DELETE /tasks/{taskId}
Authorization: Bearer <token>
```

#### 3.5 批量操作
```http
POST /tasks/batch
Authorization: Bearer <token>
Content-Type: application/json

{
  "action": "complete",
  "taskIds": ["task_id_1", "task_id_2", "task_id_3"]
}
```

### 4. 习惯追踪 API

#### 4.1 获取习惯列表
```http
GET /habits?active=true
Authorization: Bearer <token>
```

**响应:**
```json
{
  "success": true,
  "data": {
    "habits": [
      {
        "objectId": "habit_id",
        "name": "晨跑",
        "frequency": "daily",
        "targetValue": 1,
        "unit": "次",
        "icon": "figure.run",
        "color": "#FF6B6B",
        "isActive": true,
        "createdAt": "2024-01-01T00:00:00Z",
        "currentStreak": 5,
        "bestStreak": 12
      }
    ]
  }
}
```

#### 4.2 创建习惯
```http
POST /habits
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "阅读",
  "frequency": "daily",
  "targetValue": 30,
  "unit": "分钟",
  "icon": "book",
  "color": "#4ECDC4"
}
```

#### 4.3 记录习惯完成
```http
POST /habits/{habitId}/records
Authorization: Bearer <token>
Content-Type: application/json

{
  "date": "2024-01-01",
  "value": 1,
  "note": "今天跑了5公里"
}
```

#### 4.4 获取习惯统计
```http
GET /habits/{habitId}/stats?period=month
Authorization: Bearer <token>
```

**响应:**
```json
{
  "success": true,
  "data": {
    "completionRate": 0.85,
    "currentStreak": 5,
    "bestStreak": 12,
    "totalCompletions": 25,
    "averageValue": 1.2,
    "weeklyData": [
      {
        "week": "2024-W01",
        "completions": 6,
        "rate": 0.86
      }
    ]
  }
}
```

### 5. 用户管理 API

#### 5.1 获取用户信息
```http
GET /users/me
Authorization: Bearer <token>
```

#### 5.2 更新用户信息
```http
PUT /users/me
Authorization: Bearer <token>
Content-Type: application/json

{
  "username": "new_username",
  "timezone": "Asia/Shanghai",
  "preferences": {
    "theme": "dark",
    "notifications": true,
    "weekStartsOn": "monday"
  }
}
```

#### 5.3 修改密码
```http
POST /users/change-password
Authorization: Bearer <token>
Content-Type: application/json

{
  "currentPassword": "old_password",
  "newPassword": "new_password"
}
```

### 6. 数据同步 API

#### 6.1 增量同步
```http
GET /sync?lastSyncTime=2024-01-01T00:00:00Z
Authorization: Bearer <token>
```

**响应:**
```json
{
  "success": true,
  "data": {
    "tasks": {
      "created": [...],
      "updated": [...],
      "deleted": ["task_id_1", "task_id_2"]
    },
    "habits": {
      "created": [...],
      "updated": [...],
      "deleted": []
    },
    "syncTime": "2024-01-01T12:00:00Z"
  }
}
```

#### 6.2 上传本地变更
```http
POST /sync
Authorization: Bearer <token>
Content-Type: application/json

{
  "changes": [
    {
      "type": "task",
      "action": "create",
      "data": {...},
      "clientId": "local_id",
      "timestamp": "2024-01-01T10:00:00Z"
    }
  ]
}
```

### 7. 推送通知 API

#### 7.1 注册设备
```http
POST /devices
Authorization: Bearer <token>
Content-Type: application/json

{
  "deviceToken": "apns_device_token",
  "platform": "ios",
  "appVersion": "1.0.0",
  "systemVersion": "17.0"
}
```

#### 7.2 更新通知设置
```http
PUT /users/me/notifications
Authorization: Bearer <token>
Content-Type: application/json

{
  "taskReminders": true,
  "habitReminders": true,
  "dailySummary": false,
  "reminderTime": "09:00"
}
```

### 8. 错误代码定义

#### 8.1 认证错误 (4xx)
- `AUTH_REQUIRED` (401): 需要认证
- `AUTH_INVALID` (401): 认证信息无效
- `AUTH_EXPIRED` (401): 认证已过期
- `PERMISSION_DENIED` (403): 权限不足

#### 8.2 客户端错误 (4xx)
- `VALIDATION_ERROR` (400): 输入验证失败
- `RESOURCE_NOT_FOUND` (404): 资源不存在
- `DUPLICATE_RESOURCE` (409): 资源已存在
- `RATE_LIMIT_EXCEEDED` (429): 请求频率超限

#### 8.3 服务器错误 (5xx)
- `INTERNAL_ERROR` (500): 服务器内部错误
- `DATABASE_ERROR` (500): 数据库错误
- `SERVICE_UNAVAILABLE` (503): 服务不可用

### 9. API 限制和配额

#### 9.1 请求频率限制
- **认证用户**: 1000 请求/小时
- **未认证用户**: 100 请求/小时
- **批量操作**: 50 请求/小时

#### 9.2 数据限制
- **任务数量**: 免费用户最多20个活跃任务
- **习惯数量**: 免费用户最多5个活跃习惯
- **文件上传**: 单文件最大10MB
- **请求体大小**: 最大1MB

### 10. API 版本管理

#### 10.1 版本策略
- **URL版本控制**: `/v1/`, `/v2/`
- **向后兼容**: 保持至少2个版本的兼容性
- **废弃通知**: 提前3个月通知API废弃

#### 10.2 版本更新日志
- **v1.0**: 初始版本，包含基础功能
- **v1.1**: 添加批量操作支持
- **v1.2**: 增强数据同步机制

---

*文档版本: v1.0*  
*创建日期: 2024年*  
*最后更新: 2024年*
